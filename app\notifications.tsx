import { useRouter } from 'expo-router'
import React from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Text, View } from 'tamagui'
import { AppBar } from '../components/ui/AppBar'
import { AppIcons } from '../components/ui/Icons'
import { useTheme } from '../theme/DaltiThemeProvider'

export default function NotificationsScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { isDark } = useTheme()

  const screenBg = isDark ? '#0F1419' : '#FAFAFA'

  const handleBack = () => {
    router.back()
  }

  return (
    <View flex={1} backgroundColor={screenBg} style={{ paddingTop: insets.top }}>
      <AppBar
        title="Notifications"
        startIcon={{
          icon: <AppIcons.back size={20} />,
          onPress: handleBack,
        }}
      />

      <View flex={1} justifyContent="center" alignItems="center" padding="$lg">
        <View position="relative">
          <AppIcons.notifications size={64} color="$colorSecondary" />
          <View
            position="absolute"
            top={-8}
            right={-8}
            width={24}
            height={24}
            borderRadius={12}
            backgroundColor="$error"
            alignItems="center"
            justifyContent="center"
          >
            <Text fontSize={12} color="#FFFFFF" fontWeight="bold">
              3
            </Text>
          </View>
        </View>
        <Text fontSize="$headlineMedium" fontWeight="600" color="$color" marginTop="$lg">
          Notifications
        </Text>
        <Text fontSize="$bodyLarge" color="$colorSecondary" textAlign="center" marginTop="$md">
          Stay updated with your latest notifications
        </Text>
        <Text fontSize="$bodyMedium" color="$colorSecondary" textAlign="center" marginTop="$lg" opacity={0.7}>
          Coming soon...
        </Text>
      </View>
    </View>
  )
}
