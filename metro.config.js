const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// Ensure that all platforms can resolve modules from src directory
config.resolver.alias = {
  '@': './src',
  '~': './',
};

// Add support for additional file extensions
config.resolver.sourceExts.push('svg');

// Fix import.meta issues for web
config.transformer = {
  ...config.transformer,
  unstable_allowRequireContext: true,
  // Add explicit web transformer options
  web: {
    ...config.transformer.web,
    disableESTransforms: false,
  },
};

// Ensure proper module resolution for web
config.resolver.platforms = ['native', 'web', 'ios', 'android'];

// Add resolver for node_modules to handle ES modules
config.resolver.nodeModulesPaths = [
  require('path').resolve(__dirname, 'node_modules'),
];

// Add web-specific resolver options
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Add polyfills for web
config.resolver.alias = {
  ...config.resolver.alias,
  'import.meta': path.resolve(__dirname, 'web-polyfills.js'),
};

module.exports = config;
