const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');

module.exports = async function (env, argv) {
  const config = await createExpoWebpackConfigAsync(env, argv);

  // Inject polyfill at the very beginning of the entry
  if (typeof config.entry === 'string') {
    config.entry = [path.resolve(__dirname, 'polyfill-entry.js'), config.entry];
  } else if (Array.isArray(config.entry)) {
    config.entry.unshift(path.resolve(__dirname, 'polyfill-entry.js'));
  } else if (typeof config.entry === 'object') {
    Object.keys(config.entry).forEach(key => {
      if (typeof config.entry[key] === 'string') {
        config.entry[key] = [path.resolve(__dirname, 'polyfill-entry.js'), config.entry[key]];
      } else if (Array.isArray(config.entry[key])) {
        config.entry[key].unshift(path.resolve(__dirname, 'polyfill-entry.js'));
      }
    });
  }

  // Add polyfill for import.meta
  config.resolve.alias = {
    ...config.resolve.alias,
    'import.meta': require.resolve('./web-polyfills.js'),
  };

  // Add define plugin to handle import.meta
  const webpack = require('webpack');
  config.plugins.push(
    new webpack.DefinePlugin({
      'import.meta': JSON.stringify({
        url: 'file:///',
        env: process.env
      }),
      'import.meta.url': JSON.stringify('file:///'),
      'import.meta.env': JSON.stringify(process.env)
    })
  );

  // Add module rules to handle ES modules
  config.module.rules.push({
    test: /\.m?js$/,
    resolve: {
      fullySpecified: false
    }
  });

  return config;
};
