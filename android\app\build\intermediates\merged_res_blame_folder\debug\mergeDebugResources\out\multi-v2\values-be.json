{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,138,209,294,365", "endColumns": "82,70,84,70,66", "endOffsets": "133,204,289,360,427"}, "to": {"startLines": "50,71,72,74,92", "startColumns": "4,4,4,4,4", "startOffsets": "4630,7017,7088,7236,8578", "endColumns": "82,70,84,70,66", "endOffsets": "4708,7083,7168,7302,8640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1197,1264,1357,1434,1497,1580,1696,1759,1828,1887,1958,2017,2087,2161,2220,2274,2395,2456,2519,2573,2646,2768,2856,2932,3023,3104,3187,3339,3425,3512,3646,3737,3820,3877,3928,3994,4066,4143,4214,4297,4372,4449,4531,4607,4715,4804,4886,4977,5073,5147,5228,5323,5377,5459,5525,5612,5698,5760,5824,5887,5956,6066,6179,6282,6389,6492,6589,6650,6705,6785,6870,6946", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,82,115,62,68,58,70,58,69,73,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,133,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,102,96,60,54,79,84,75,78", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1192,1259,1352,1429,1492,1575,1691,1754,1823,1882,1953,2012,2082,2156,2215,2269,2390,2451,2514,2568,2641,2763,2851,2927,3018,3099,3182,3334,3420,3507,3641,3732,3815,3872,3923,3989,4061,4138,4209,4292,4367,4444,4526,4602,4710,4799,4881,4972,5068,5142,5223,5318,5372,5454,5520,5607,5693,5755,5819,5882,5951,6061,6174,6277,6384,6487,6584,6645,6700,6780,6865,6941,7020"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,54,55,56,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,5028,5106,5173,6940,7173,7307,7390,7506,7569,7638,7697,7768,7827,7897,7971,8030,8084,8205,8266,8329,8383,8456,8645,8733,8809,8900,8981,9064,9216,9302,9389,9523,9614,9697,9754,9805,9871,9943,10020,10091,10174,10249,10326,10408,10484,10592,10681,10763,10854,10950,11024,11105,11200,11254,11336,11402,11489,11575,11637,11701,11764,11833,11943,12056,12159,12266,12369,12466,12527,12582,12744,12829,12905", "endLines": "7,35,36,37,38,39,47,48,49,54,55,56,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,142,143,144", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,82,115,62,68,58,70,58,69,73,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,133,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,102,96,60,54,79,84,75,78", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,5101,5168,5261,7012,7231,7385,7501,7564,7633,7692,7763,7822,7892,7966,8025,8079,8200,8261,8324,8378,8451,8573,8728,8804,8895,8976,9059,9211,9297,9384,9518,9609,9692,9749,9800,9866,9938,10015,10086,10169,10244,10321,10403,10479,10587,10676,10758,10849,10945,11019,11100,11195,11249,11331,11397,11484,11570,11632,11696,11759,11828,11938,12051,12154,12261,12364,12461,12522,12577,12657,12824,12900,12979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,261,391,542,681,810,937,1088,1187,1329,1481", "endColumns": "111,93,129,150,138,128,126,150,98,141,151,126", "endOffsets": "162,256,386,537,676,805,932,1083,1182,1324,1476,1603"}, "to": {"startLines": "51,53,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4713,4934,5593,5723,5874,6013,6142,6269,6420,6519,6661,6813", "endColumns": "111,93,129,150,138,128,126,150,98,141,151,126", "endOffsets": "4820,5023,5718,5869,6008,6137,6264,6415,6514,6656,6808,6935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,12984", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,13080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,12662", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,12739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "52,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4825,5266,5374,5486", "endColumns": "108,107,111,106", "endOffsets": "4929,5369,5481,5588"}}]}]}