org.adscloud.dalti.provider.app-lifecycle-livedata-2.8.7-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\058e187eae7847e7e580e42d06f70a5b\transformed\lifecycle-livedata-2.8.7\res
org.adscloud.dalti.provider.app-material-1.13.0-alpha10-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\10c7247ae1e51dbe3d3ee369ed79f4bb\transformed\material-1.13.0-alpha10\res
org.adscloud.dalti.provider.app-lifecycle-runtime-release-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\12552f97a074c8fe45dd93887e193c08\transformed\lifecycle-runtime-release\res
org.adscloud.dalti.provider.app-frameanimation-3.0.5-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\13539e054fd2491c95feae4e914c7ab8\transformed\frameanimation-3.0.5\res
org.adscloud.dalti.provider.app-glide-plugin-3.0.5-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\171d3c8c60c980d67e5dba9b3903c12f\transformed\glide-plugin-3.0.5\res
org.adscloud.dalti.provider.app-tracing-ktx-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\18cdabc45926f1dc3dff94376b7201d0\transformed\tracing-ktx-1.2.0\res
org.adscloud.dalti.provider.app-constraintlayout-2.0.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\res
org.adscloud.dalti.provider.app-startup-runtime-1.1.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\res
org.adscloud.dalti.provider.app-core-runtime-2.2.0-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\274a5e4c52765583c47c315e8a011727\transformed\core-runtime-2.2.0\res
org.adscloud.dalti.provider.app-coordinatorlayout-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\28bc247258e5cff6e6aac54078e1de43\transformed\coordinatorlayout-1.2.0\res
org.adscloud.dalti.provider.app-BlurView-version-2.0.6-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\res
org.adscloud.dalti.provider.app-cardview-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba03b814d30daf0b666892538f8bffe\transformed\cardview-1.0.0\res
org.adscloud.dalti.provider.app-fragment-ktx-1.8.6-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2c1b72331a78b77e664ea1083cd67d19\transformed\fragment-ktx-1.8.6\res
org.adscloud.dalti.provider.app-activity-1.8.1-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbc710fea20a8c7a1626c685301533b\transformed\activity-1.8.1\res
org.adscloud.dalti.provider.app-datastore-core-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbf38e2d30d78a360ad4a1f6b882fff\transformed\datastore-core-release\res
org.adscloud.dalti.provider.app-profileinstaller-1.4.1-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\res
org.adscloud.dalti.provider.app-lifecycle-viewmodel-release-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\332889fa8b3daea987c5f18a3d5cb575\transformed\lifecycle-viewmodel-release\res
org.adscloud.dalti.provider.app-datastore-release-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\3c4742b077e114f2cb3b61b3111e0a85\transformed\datastore-release\res
org.adscloud.dalti.provider.app-androidsvg-aar-1.4-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\3f316ee5f992791852f7290c08120ef5\transformed\androidsvg-aar-1.4\res
org.adscloud.dalti.provider.app-drawerlayout-1.2.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\4622975830a9e5907499b239e943e50d\transformed\drawerlayout-1.2.0\res
org.adscloud.dalti.provider.app-expo.modules.splashscreen-0.30.10-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\4706a4f10ccde4f67225980dba6215a7\transformed\expo.modules.splashscreen-0.30.10\res
org.adscloud.dalti.provider.app-lifecycle-viewmodel-ktx-2.8.7-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ada8695c06c9f4e7b207ffe90e136ab\transformed\lifecycle-viewmodel-ktx-2.8.7\res
org.adscloud.dalti.provider.app-lifecycle-service-2.8.7-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b9ab513ab389f01d99fe91df9157697\transformed\lifecycle-service-2.8.7\res
org.adscloud.dalti.provider.app-appcompat-1.7.0-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\4bf03df1e227318d4ab7c4ce04613cbc\transformed\appcompat-1.7.0\res
org.adscloud.dalti.provider.app-emoji2-views-helper-1.3.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\51166ce65ea68e12719e2bca48ab2cfd\transformed\emoji2-views-helper-1.3.0\res
org.adscloud.dalti.provider.app-lifecycle-process-2.8.7-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\541598e8955756eef00aac9df9aeff7e\transformed\lifecycle-process-2.8.7\res
org.adscloud.dalti.provider.app-recyclerview-1.1.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\59364def5c2f903fdc1dceeb75244942\transformed\recyclerview-1.1.0\res
org.adscloud.dalti.provider.app-activity-ktx-1.8.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\64ce65a8466cfe96f1dba4fff102a7b0\transformed\activity-ktx-1.8.1\res
org.adscloud.dalti.provider.app-emoji2-1.3.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\res
org.adscloud.dalti.provider.app-viewpager2-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\6bc00ab5eaca23c3c3ebd49ed8511ff9\transformed\viewpager2-1.0.0\res
org.adscloud.dalti.provider.app-drawee-3.6.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\6fcecc4c6bb8b8753bdcf3c3459c4fbc\transformed\drawee-3.6.0\res
org.adscloud.dalti.provider.app-fragment-1.8.6-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\72e7ef9aad1048527f49ff1cef8d3a5f\transformed\fragment-1.8.6\res
org.adscloud.dalti.provider.app-graphics-shapes-release-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\75ff22a13f4d65a41661ee864569ec00\transformed\graphics-shapes-release\res
org.adscloud.dalti.provider.app-core-1.16.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\res
org.adscloud.dalti.provider.app-savedstate-ktx-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\829cfe53c64cfc693496f8451f29e7a8\transformed\savedstate-ktx-1.2.1\res
org.adscloud.dalti.provider.app-expo.modules.filesystem-18.1.11-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\res
org.adscloud.dalti.provider.app-core-viewtree-1.0.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\90286ba6bda662a1dd54b2898bd8b82b\transformed\core-viewtree-1.0.0\res
org.adscloud.dalti.provider.app-lifecycle-runtime-ktx-release-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc8753c6c46248fdb103d346c33a77a\transformed\lifecycle-runtime-ktx-release\res
org.adscloud.dalti.provider.app-lifecycle-livedata-core-ktx-2.8.7-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\9f86ddb12b4bd2e6c7bfe074aa814758\transformed\lifecycle-livedata-core-ktx-2.8.7\res
org.adscloud.dalti.provider.app-swiperefreshlayout-1.1.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\a177d82bc21b186c301bc9b6c8d7387d\transformed\swiperefreshlayout-1.1.0\res
org.adscloud.dalti.provider.app-avif-3.0.5-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\a30433f5792386d46025b522bebb1c74\transformed\avif-3.0.5\res
org.adscloud.dalti.provider.app-apng-3.0.5-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\a498e18e820d2039082300a9cd59b4c3\transformed\apng-3.0.5\res
org.adscloud.dalti.provider.app-gif-3.0.5-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac1d1d616d79aa20e507585d1621bf46\transformed\gif-3.0.5\res
org.adscloud.dalti.provider.app-expo.modules.systemui-5.0.11-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac36f880526720140420368ddc4d5355\transformed\expo.modules.systemui-5.0.11\res
org.adscloud.dalti.provider.app-autofill-1.1.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\b52e52fc777190b4295f59d0a858517f\transformed\autofill-1.1.0\res
org.adscloud.dalti.provider.app-transition-1.5.0-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\c2aaf2f0c5250c0e148ab60ada39c9da\transformed\transition-1.5.0\res
org.adscloud.dalti.provider.app-lifecycle-livedata-core-2.8.7-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4281a2ac61535291daa33ed3a152892\transformed\lifecycle-livedata-core-2.8.7\res
org.adscloud.dalti.provider.app-glide-4.16.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\c5529e502845fdf46b49c2089dbb4832\transformed\glide-4.16.0\res
org.adscloud.dalti.provider.app-coil-core-release-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fa3063abde87513a96b195fbd99e0c\transformed\coil-core-release\res
org.adscloud.dalti.provider.app-annotation-experimental-1.4.1-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\c72240e70788902de7eb47bfe9d21a7b\transformed\annotation-experimental-1.4.1\res
org.adscloud.dalti.provider.app-biometric-1.1.0-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\res
org.adscloud.dalti.provider.app-appcompat-resources-1.7.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\cafe38d429a8d27ed8b0af5abbc941f2\transformed\appcompat-resources-1.7.0\res
org.adscloud.dalti.provider.app-lifecycle-viewmodel-savedstate-2.8.7-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd935b6ef69c69039cddac913e02da3f\transformed\lifecycle-viewmodel-savedstate-2.8.7\res
org.adscloud.dalti.provider.app-react-android-0.79.5-debug-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\res
org.adscloud.dalti.provider.app-core-ktx-1.16.0-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1e8a26aa809009c2a79370642d61d\transformed\core-ktx-1.16.0\res
org.adscloud.dalti.provider.app-browser-1.6.0-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa8d219fb756bbf0447e8d2f088c459\transformed\browser-1.6.0\res
org.adscloud.dalti.provider.app-webkit-1.14.0-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\e0744e17698f09f9c09091ef16a6a923\transformed\webkit-1.14.0\res
org.adscloud.dalti.provider.app-media-1.0.0-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\e2740c2d15f5f0e1c1cc73d5e9e0b8e0\transformed\media-1.0.0\res
org.adscloud.dalti.provider.app-awebp-3.0.5-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\e5e086c331edf8b998f04164b0a2170a\transformed\awebp-3.0.5\res
org.adscloud.dalti.provider.app-core-splashscreen-1.2.0-alpha02-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\ed280626aa95171682096e5872c6af69\transformed\core-splashscreen-1.2.0-alpha02\res
org.adscloud.dalti.provider.app-datastore-preferences-release-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\f51ce7157b1bae6e240e4502454f6302\transformed\datastore-preferences-release\res
org.adscloud.dalti.provider.app-tracing-1.2.0-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\f7ab8f7120f36931c3468a226fbeffc5\transformed\tracing-1.2.0\res
org.adscloud.dalti.provider.app-savedstate-1.2.1-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc1cc582935f0fa4fa7ad13b3b8686c\transformed\savedstate-1.2.1\res
org.adscloud.dalti.provider.app-pngs-63 D:\reactnative adscloud\dalti_provider_2\android\app\build\generated\res\pngs\debug
org.adscloud.dalti.provider.app-resValues-64 D:\reactnative adscloud\dalti_provider_2\android\app\build\generated\res\resValues\debug
org.adscloud.dalti.provider.app-packageDebugResources-65 D:\reactnative adscloud\dalti_provider_2\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
org.adscloud.dalti.provider.app-packageDebugResources-66 D:\reactnative adscloud\dalti_provider_2\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
org.adscloud.dalti.provider.app-debug-67 D:\reactnative adscloud\dalti_provider_2\android\app\build\intermediates\merged_res\debug\mergeDebugResources
org.adscloud.dalti.provider.app-debug-68 D:\reactnative adscloud\dalti_provider_2\android\app\src\debug\res
org.adscloud.dalti.provider.app-main-69 D:\reactnative adscloud\dalti_provider_2\android\app\src\main\res
org.adscloud.dalti.provider.app-debug-70 D:\reactnative adscloud\dalti_provider_2\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-71 D:\reactnative adscloud\dalti_provider_2\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-72 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-73 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-74 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-75 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-76 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-77 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-78 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-79 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-80 D:\reactnative adscloud\dalti_provider_2\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-81 D:\reactnative adscloud\dalti_provider_2\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-82 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-bottom-tabs\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-83 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-84 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-85 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-keychain\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-86 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-mmkv\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-87 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-88 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-89 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-90 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-91 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.provider.app-debug-92 D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-worklets\android\build\intermediates\packaged_res\debug\packageDebugResources
