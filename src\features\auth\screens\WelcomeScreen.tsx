import { Text, View } from '@tamagui/core'
import { useRouter } from 'expo-router'
import React from 'react'
import { ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { DaltiLogo } from '../../../../components/ui/Icons'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../../../../theme/themes'

export default function WelcomeScreen() {
  const router = useRouter()
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme
  const insets = useSafeAreaInsets()

  const handleLogin = () => {
    router.push('/login')
  }

  const handleSignUp = () => {
    router.push('/register')
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: currentTheme.background,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader />

      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
        <View
          flex={1}
          paddingHorizontal="$lg"
          paddingVertical="$xl"
          alignItems="center"
          justifyContent="space-between"
        >
          {/* Main content */}
          <View flex={1} alignItems="center" justifyContent="center" gap="$2xl">
            {/* Logo */}
            <DaltiLogo size={100} variant={isDark ? 'dark' : 'white'} />

            {/* Welcome title */}
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color="$color"
              textAlign="center"
              marginTop="$xl"
            >
              Welcome to Dalti
            </Text>

            {/* Description */}
            <Text
              fontSize="$bodyLarge"
              color="$colorSecondary"
              textAlign="center"
              lineHeight={24}
              paddingHorizontal="$md"
            >
              Manage your schedule and customers with ease. Connect with clients, book appointments.
            </Text>
          </View>

          {/* Bottom section */}
          <View width="100%" gap="$lg" paddingBottom="$xl">
            {/* Login Button */}
            <Button
              title="Log In"
              variant="primary"
              size="lg"
              fullWidth
              onPress={handleLogin}
            />

            {/* Sign Up Button */}
            <Button
              title="Sign Up"
              variant="outline"
              size="lg"
              fullWidth
              onPress={handleSignUp}
            />

            {/* Icons Showcase Button */}
            {/* <Button
              title="View Icons Library"
              variant="ghost"
              size="md"
              fullWidth
              onPress={handleIconsPress}
            /> */}

            {/* Terms and Privacy */}
            {/* <View alignItems="center" marginTop="$md">
              <Text
                fontSize="$bodySmall"
                color="$colorSecondary"
                textAlign="center"
                lineHeight={18}
              >
                By continuing, you agree to our{' '}
                <Pressable onPress={handleTermsPress}>
                  <Text
                    fontSize="$bodySmall"
                    color="$primary"
                    textDecorationLine="underline"
                  >
                    Terms of Service
                  </Text>
                </Pressable>
                {' '}and{' '}
                <Pressable onPress={handlePrivacyPress}>
                  <Text
                    fontSize="$bodySmall"
                    color="$primary"
                    textDecorationLine="underline"
                  >
                    Privacy Policy
                  </Text>
                </Pressable>
                .
              </Text>
            </View> */}
          </View>
        </View>
      </ScrollView>
    </View>
  )
}
