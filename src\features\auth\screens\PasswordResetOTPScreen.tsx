import { Text, View } from '@tamagui/core'
import { useLocalSearchParams, useRouter } from 'expo-router'
import React, { useEffect, useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { Input } from '../../../../components/ui/Input'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../../../../theme/themes'
import { AuthService } from '../services/auth.service'

export default function PasswordResetOTPScreen() {
  const router = useRouter()
  const params = useLocalSearchParams()
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme
  const insets = useSafeAreaInsets()
  const [otp, setOtp] = useState('')
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const [loading, setLoading] = useState(false)

  const email = params.email as string

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#257587',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#15424E',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  // Countdown timer for resend OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [countdown])

  const handleBack = () => {
    router.back()
  }

  const handleVerifyOTP = async () => {
    if (!otp.trim() || otp.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code')
      return
    }

    setLoading(true)

    try {
      const authService = AuthService.getInstance()
      const response = await authService.verifyPasswordResetOTP({
        otp: otp.trim(),
        email: email
      })

      // Check for success - either response.success is true OR response has resetToken (indicating success)
      if ((response.success && response.data?.token) || response.resetToken) {
        // Navigate to reset password screen with token
        const token = response.data?.token || response.resetToken;
        router.push({
          pathname: '/reset-password',
          params: { token: token }
        })
      } else {
        Alert.alert('Verification Failed', response.message || 'Invalid OTP')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (!canResend) return

    try {
      setLoading(true)
      const authService = AuthService.getInstance()
      const response = await authService.forgotPassword(email)
      
      if (response.success) {
        setCountdown(60)
        setCanResend(false)
        Alert.alert('Success', 'OTP sent successfully!')
      } else {
        Alert.alert('Error', response.message || 'Failed to resend OTP')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to resend OTP')
    } finally {
      setLoading(false)
    }
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Verify Reset Code
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
            >
              We sent a 6-digit code to {email}
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* OTP Input */}
              <Input
                label="Verification Code"
                placeholder="Enter 6-digit code"
                value={otp}
                onChangeText={setOtp}
                keyboardType="number-pad"
                maxLength={6}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
                textAlign="center"
                fontSize="$headlineSmall"
                letterSpacing={4}
              />

              {/* Verify Button */}
              <Button
                title="Verify Code"
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                onPress={handleVerifyOTP}
                containerStyle={{ marginTop: 16 }}
              />

              {/* Resend OTP */}
              <View alignItems="center" marginTop="$lg">
                <Text
                  fontSize="$bodyMedium"
                  color={authColors.secondaryText}
                  textAlign="center"
                  marginBottom="$sm"
                >
                  Didn't receive the code?
                </Text>
                
                {canResend ? (
                  <Pressable onPress={handleResendOTP}>
                    <Text
                      fontSize="$bodyMedium"
                      color={authColors.linkColor}
                      fontWeight="semibold"
                    >
                      Resend Code
                    </Text>
                  </Pressable>
                ) : (
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.secondaryText}
                  >
                    Resend in {countdown}s
                  </Text>
                )}
              </View>

              {/* Back to Login */}
              <View alignItems="center" marginTop="$lg">
                <Pressable onPress={() => router.push('/login')}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Back to Login
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
