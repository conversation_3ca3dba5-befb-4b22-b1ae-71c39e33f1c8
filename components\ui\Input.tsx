import { Text, View } from '@tamagui/core'
import React, { forwardRef, useState } from 'react'
import { Pressable, TextInput, TextInputProps } from 'react-native'
import { useTheme } from '../../theme/DaltiThemeProvider'

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string
  placeholder?: string
  error?: string
  helperText?: string
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'outlined' | 'filled'
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  onRightIconPress?: () => void
  required?: boolean
  containerStyle?: any
}

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  placeholder,
  error,
  helperText,
  disabled = false,
  size = 'md',
  variant = 'outlined',
  leftIcon,
  rightIcon,
  onRightIconPress,
  required = false,
  containerStyle,
  onFocus,
  onBlur,
  ...textInputProps
}, ref) => {
  const { isDark } = useTheme()
  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = (e: any) => {
    setIsFocused(true)
    onFocus?.(e)
  }

  const handleBlur = (e: any) => {
    setIsFocused(false)
    onBlur?.(e)
  }

  // Size configurations
  const sizeConfig = {
    sm: {
      height: '$inputSm',
      fontSize: '$bodySmall',
      paddingHorizontal: '$md',
      iconSize: '$iconSm',
    },
    md: {
      height: '$input',
      fontSize: '$bodyMedium',
      paddingHorizontal: '$lg',
      iconSize: '$iconMd',
    },
    lg: {
      height: '$inputLg',
      fontSize: '$bodyLarge',
      paddingHorizontal: '$lg',
      iconSize: '$iconLg',
    },
  }

  const currentSize = sizeConfig[size]

  // Get border color based on state
  const getBorderColor = () => {
    if (error) return '$inputBorderColorError'
    if (isFocused) return '$inputBorderColorFocus'
    if (disabled) return '$inputBorderColor'
    return '$inputBorderColor'
  }

  // Get background color based on variant and state
  const getBackgroundColor = () => {
    if (disabled) return '$inputBackgroundDisabled'
    if (variant === 'filled') return '$inputBackground'
    if (isFocused && variant === 'outlined') return '$inputBackgroundFocus'
    return '$inputBackground'
  }

  return (
    <View gap="$xs" style={containerStyle}>
      {/* Label */}
      {label && (
        <Text 
          fontSize="$labelMedium" 
          fontWeight="semibold" 
          color={error ? '$inputBorderColorError' : '$color'}
        >
          {label}
          {required && (
            <Text color="$inputBorderColorError"> *</Text>
          )}
        </Text>
      )}

      {/* Input Container */}
      <View
        position="relative"
        height={currentSize.height}
        backgroundColor={getBackgroundColor()}
        borderWidth={variant === 'outlined' ? 1 : 0}
        borderColor={getBorderColor()}
        borderRadius="$input"
        flexDirection="row"
        alignItems="center"
        opacity={disabled ? 0.6 : 1}
      >
        {/* Left Icon */}
        {leftIcon && (
          <View
            padding="$md"
            justifyContent="center"
            alignItems="center"
          >
            <View
              width={36}
              height={36}
              backgroundColor={isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}
              borderRadius="$md"
              justifyContent="center"
              alignItems="center"
            >
              {leftIcon}
            </View>
          </View>
        )}

        {/* Text Input */}
        <TextInput
          ref={ref}
          placeholder={placeholder}
          placeholderTextColor={isDark ? '#6C7278' : '#A0A0A0'}
          editable={!disabled}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={{
            flex: 1,
            height: '100%',
            fontSize: 14, // Will be overridden by theme
            color: isDark ? '#F8F9FA' : '#2C3E50',
            paddingHorizontal: leftIcon || rightIcon ? 8 : 16,
            paddingLeft: leftIcon ? 8 : 16,
            paddingRight: rightIcon ? 8 : 16,
          }}
          {...textInputProps}
        />

        {/* Right Icon */}
        {rightIcon && (
          <View
            padding="$md"
            justifyContent="center"
            alignItems="center"
          >
            <Pressable
              onPress={onRightIconPress}
              disabled={!onRightIconPress}
            >
              <View
                width={36}
                height={36}
                backgroundColor={isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}
                borderRadius="$md"
                justifyContent="center"
                alignItems="center"
              >
                {rightIcon}
              </View>
            </Pressable>
          </View>
        )}
      </View>

      {/* Helper Text or Error */}
      {(error || helperText) && (
        <Text 
          fontSize="$labelSmall" 
          color={error ? '$inputBorderColorError' : '$colorSecondary'}
        >
          {error || helperText}
        </Text>
      )}
    </View>
  )
})

Input.displayName = 'Input'
