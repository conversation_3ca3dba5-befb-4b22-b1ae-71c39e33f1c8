import { BottomTabBarProps } from '@react-navigation/bottom-tabs'
import React from 'react'
import { Pressable, StyleSheet } from 'react-native'
import { Text, View } from 'tamagui'
import { useTheme } from '../../theme/DaltiThemeProvider'
import { AppIcons } from './Icons'

interface TabBarIconProps {
  name: string
  focused: boolean
  color: string
  size: number
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />
    case 'clients':
      return <AppIcons.people size={size} color={color} />
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />
    default:
      return <AppIcons.home size={size} color={color} />
  }
}

export const CustomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  const { theme, isDark } = useTheme() // This ensures re-render on theme change

  // Use hardcoded theme-aware colors that match our design system
  // This ensures proper theme switching regardless of Tamagui token issues
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF'
  const activeColor = isDark ? '#257587' : '#15424E'  // Use primary colors from brand system
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D'
  const centerButtonBg = isDark ? '#257587' : '#15424E'
  const borderColor = isDark ? '#3A4048' : '#E1E8ED'



  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home'
      case 'calendar':
        return 'Calendar'
      case 'clients':
        return 'Clients'
      case 'bookings':
        return 'Bookings'
      default:
        return routeName
    }
  }

  return (
    <View style={[styles.tabBar, { backgroundColor: tabBarBg, borderTopColor: borderColor }]}>
      {/* Left tabs */}
      <View style={styles.leftTabs}>
        {state.routes.slice(0, 2).map((route, index) => {
          const { options } = descriptors[route.key]
          const isFocused = state.index === index

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            })

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name)
            }
          }

          return (
            <Pressable
              key={route.key}
              onPress={onPress}
              style={styles.tab}
            >
              <TabBarIcon
                name={route.name}
                focused={isFocused}
                color={isFocused ? activeColor : inactiveColor}
                size={20}
              />
              <Text
                fontSize={12}
                color={isFocused ? activeColor : inactiveColor}
                fontWeight={isFocused ? '600' : '400'}
                marginTop={2}
              >
                {getTabLabel(route.name)}
              </Text>
            </Pressable>
          )
        })}
      </View>

      {/* Center floating action button with circular container */}
      <View style={styles.centerButtonContainer}>
        {/* Circular background container */}
        <View style={[styles.centerButtonBackground, { backgroundColor: tabBarBg, borderColor: borderColor }]}>
          <Pressable
            style={[styles.centerButton, { backgroundColor: centerButtonBg }]}
            onPress={() => console.log('QR Code scanner pressed')}
          >
            <AppIcons.qrCode size={24} color="#FFFFFF" />
          </Pressable>
        </View>
      </View>

      {/* Right tabs */}
      <View style={styles.rightTabs}>
        {state.routes.slice(2, 4).map((route, index) => {
          const actualIndex = index + 2
          const { options } = descriptors[route.key]
          const isFocused = state.index === actualIndex

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            })

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name)
            }
          }

          return (
            <Pressable
              key={route.key}
              onPress={onPress}
              style={styles.tab}
            >
              <TabBarIcon
                name={route.name}
                focused={isFocused}
                color={isFocused ? activeColor : inactiveColor}
                size={20}
              />
              <Text
                fontSize={12}
                color={isFocused ? activeColor : inactiveColor}
                fontWeight={isFocused ? '600' : '400'}
                marginTop={2}
              >
                {getTabLabel(route.name)}
              </Text>
            </Pressable>
          )
        })}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: 80,
    paddingBottom: 20,
    paddingTop: 10,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative', // Ensure proper positioning context for absolute children
  },
  leftTabs: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-around',
    alignItems: 'center',  // Ensure vertical centering
  },
  rightTabs: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-around',
    alignItems: 'center',  // Ensure vertical centering
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,  // Reduced padding for better centering
    minHeight: 50,       // Ensure consistent height for all tabs
  },
  centerButtonContainer: {
    position: 'absolute',
    top: -25,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  centerButtonBackground: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  centerButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
})
