{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1389,1476,1540,1602,1666,1734,1799,1865,1933,1989,2043,2152,2210,2272,2326,2401,2521,2603,2680,2770,2854,2934,3068,3146,3226,3349,3437,3515,3569,3620,3686,3754,3828,3899,3975,4046,4124,4194,4264,4364,4453,4531,4619,4707,4779,4851,4937,4988,5066,5132,5213,5296,5358,5422,5485,5554,5654,5758,5851,5951,6043,6131,6189,6244,6322,6406,6484", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,79,86,63,61,63,67,64,65,67,55,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,87,71,71,85,50,77,65,80,82,61,63,62,68,99,103,92,99,91,87,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1384,1471,1535,1597,1661,1729,1794,1860,1928,1984,2038,2147,2205,2267,2321,2396,2516,2598,2675,2765,2849,2929,3063,3141,3221,3344,3432,3510,3564,3615,3681,3749,3823,3894,3970,4041,4119,4189,4259,4359,4448,4526,4614,4702,4774,4846,4932,4983,5061,5127,5208,5291,5353,5417,5480,5549,5649,5753,5846,5946,6038,6126,6184,6239,6317,6401,6479,6551"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,4678,4737,4801,6520,6588,6648,6728,6815,6879,6941,7005,7073,7138,7204,7272,7328,7382,7491,7549,7611,7665,7740,7860,7942,8019,8109,8193,8273,8407,8485,8565,8688,8776,8854,8908,8959,9025,9093,9167,9238,9314,9385,9463,9533,9603,9703,9792,9870,9958,10046,10118,10190,10276,10327,10405,10471,10552,10635,10697,10761,10824,10893,10993,11097,11190,11290,11382,11470,11528,11583,11741,11825,11903", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,79,86,63,61,63,67,64,65,67,55,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,87,71,71,85,50,77,65,80,82,61,63,62,68,99,103,92,99,91,87,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,4732,4796,4888,6583,6643,6723,6810,6874,6936,7000,7068,7133,7199,7267,7323,7377,7486,7544,7606,7660,7735,7855,7937,8014,8104,8188,8268,8402,8480,8560,8683,8771,8849,8903,8954,9020,9088,9162,9233,9309,9380,9458,9528,9598,9698,9787,9865,9953,10041,10113,10185,10271,10322,10400,10466,10547,10630,10692,10756,10819,10888,10988,11092,11185,11285,11377,11465,11523,11578,11656,11820,11898,11970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,11661", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,11736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,11975", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,12071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,249,371,515,650,786,913,1054,1154,1295,1439", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "156,244,366,510,645,781,908,1049,1149,1290,1434,1561"}, "to": {"startLines": "48,50,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4590,5203,5325,5469,5604,5740,5867,6008,6108,6249,6393", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "4475,4673,5320,5464,5599,5735,5862,6003,6103,6244,6388,6515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "49,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4480,4893,4994,5106", "endColumns": "109,100,111,96", "endOffsets": "4585,4989,5101,5198"}}]}]}