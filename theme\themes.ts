import { tokens } from './tokens'

// Light Theme
export const lightTheme = {
  // Background colors
  background: '#FFFFFF',
  backgroundHover: '#FAFAFA',
  backgroundPress: '#F5F5F5',
  backgroundFocus: '#FAFAFA',
  backgroundStrong: '#2C3E50',
  backgroundTransparent: 'transparent',
  
  // Surface colors
  surface: '#FAFAFA',
  surfaceHover: '#F0F0F0',
  surfacePress: '#E8E8E8',
  surfaceFocus: '#F0F0F0',
  
  // Primary colors
  primary: tokens.color.primary,
  primaryHover: tokens.color.primaryVariant,
  primaryPress: '#0A2B31',
  primaryFocus: tokens.color.primaryVariant,
  
  // Secondary colors
  secondary: tokens.color.secondary,
  secondaryHover: '#3ABAB3',
  secondaryPress: '#2BA59E',
  secondaryFocus: '#3ABAB3',
  
  // Accent colors
  accent: tokens.color.accent,
  accentHover: '#FFD93D',
  accentPress: '#E6C75A',
  accentFocus: '#FFD93D',
  
  // Text colors
  color: tokens.color.textPrimary,
  colorHover: tokens.color.textPrimary,
  colorPress: tokens.color.textPrimary,
  colorFocus: tokens.color.textPrimary,
  colorTransparent: 'transparent',
  
  // Secondary text
  colorSecondary: tokens.color.textSecondary,
  
  // Text on colored backgrounds
  colorOnPrimary: tokens.color.textOnPrimary,
  colorOnSecondary: tokens.color.textPrimary,
  colorOnAccent: tokens.color.textPrimary,
  
  // Status colors
  success: tokens.color.success,
  successHover: '#229954',
  successPress: '#1E8449',
  
  error: tokens.color.error,
  errorHover: '#C0392B',
  errorPress: '#A93226',
  
  warning: tokens.color.warning,
  warningHover: '#D68910',
  warningPress: '#B7950B',
  
  // Status indicators
  statusActive: tokens.color.statusActive,
  statusInactive: tokens.color.statusInactive,
  statusBlocked: tokens.color.statusBlocked,
  statusPending: tokens.color.statusPending,
  
  // Border colors
  borderColor: '#E1E8ED',
  borderColorHover: '#D1D9E0',
  borderColorPress: '#C1C9D0',
  borderColorFocus: tokens.color.primary,

  // Input specific colors
  inputBackground: '#FFFFFF',                        // Pure white for better contrast on #FAFAFA
  inputBackgroundFocus: '#FFFFFF',                   // Keep white on focus
  inputBackgroundDisabled: '#F8F9FA',
  inputBorderColor: 'rgba(21, 66, 78, 0.2)',        // Light teal border for better visibility
  inputBorderColorHover: 'rgba(21, 66, 78, 0.3)',   // Slightly darker on hover
  inputBorderColorFocus: tokens.color.primary,       // Full primary color on focus
  inputBorderColorError: tokens.color.error,
  inputPlaceholderColor: '#A0A0A0',
  inputTextColor: tokens.color.textPrimary,
  inputTextColorDisabled: '#A0A0A0',

  // Shadow colors
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  shadowColorStrong: 'rgba(0, 0, 0, 0.2)',
}

// Dark Theme
export const darkTheme = {
  // Background colors
  background: '#0F1419',
  backgroundHover: '#1C2127',
  backgroundPress: '#151B20',
  backgroundFocus: '#1C2127',
  backgroundStrong: '#F8F9FA',
  backgroundTransparent: 'transparent',
  
  // Surface colors
  surface: '#1C2127',
  surfaceHover: '#242A30',
  surfacePress: '#1A2026',
  surfaceFocus: '#242A30',
  
  // Primary colors
  primary: tokens.color.darkPrimary,
  primaryHover: '#2A8599',
  primaryPress: '#1F6B7A',
  primaryFocus: '#2A8599',
  
  // Secondary colors
  secondary: tokens.color.darkSecondary,
  secondaryHover: '#4AC4BD',
  secondaryPress: '#2FA59E',
  secondaryFocus: '#4AC4BD',
  
  // Accent colors
  accent: tokens.color.accent,
  accentHover: '#FFD93D',
  accentPress: '#E6C75A',
  accentFocus: '#FFD93D',
  
  // Text colors
  color: tokens.color.darkOnSurface,
  colorHover: tokens.color.darkOnSurface,
  colorPress: tokens.color.darkOnSurface,
  colorFocus: tokens.color.darkOnSurface,
  colorTransparent: 'transparent',
  
  // Secondary text
  colorSecondary: tokens.color.darkOnSurfaceVariant,
  
  // Text on colored backgrounds
  colorOnPrimary: tokens.color.darkOnPrimary,
  colorOnSecondary: tokens.color.textPrimary,
  colorOnAccent: tokens.color.textPrimary,
  
  // Status colors
  success: tokens.color.success,
  successHover: '#2ECC71',
  successPress: '#229954',
  
  error: tokens.color.error,
  errorHover: '#EC7063',
  errorPress: '#C0392B',
  
  warning: tokens.color.warning,
  warningHover: '#F7DC6F',
  warningPress: '#D68910',
  
  // Status indicators
  statusActive: tokens.color.statusActive,
  statusInactive: tokens.color.statusInactive,
  statusBlocked: tokens.color.statusBlocked,
  statusPending: tokens.color.statusPending,
  
  // Border colors
  borderColor: '#3A4048',
  borderColorHover: '#4A5058',
  borderColorPress: '#5A6068',
  borderColorFocus: tokens.color.darkPrimary,

  // Input specific colors
  inputBackground: 'rgba(58, 64, 72, 0.3)',          // Semi-transparent for better contrast on #1C2127
  inputBackgroundFocus: 'rgba(58, 64, 72, 0.4)',     // Slightly more opaque on focus
  inputBackgroundDisabled: tokens.color.darkInactiveElement,
  inputBorderColor: 'rgba(37, 117, 135, 0.3)',      // Dark teal border for better visibility
  inputBorderColorHover: 'rgba(37, 117, 135, 0.4)',  // Slightly brighter on hover
  inputBorderColorFocus: tokens.color.darkPrimary,    // Full primary color on focus
  inputBorderColorError: tokens.color.error,
  inputPlaceholderColor: '#6C7278',
  inputTextColor: tokens.color.darkOnSurface,
  inputTextColorDisabled: '#6C7278',

  // Shadow colors
  shadowColor: 'rgba(0, 0, 0, 0.3)',
  shadowColorStrong: 'rgba(0, 0, 0, 0.5)',
}

// Theme configuration
export const themes = {
  light: lightTheme,
  dark: darkTheme,
}
