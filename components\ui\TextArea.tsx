import React, { forwardRef, useState } from 'react'
import { TextInput, TextInputProps } from 'react-native'
import { View, Text } from '@tamagui/core'
import { useTheme } from '../../theme/DaltiThemeProvider'

export interface TextAreaProps extends Omit<TextInputProps, 'style' | 'multiline'> {
  label?: string
  placeholder?: string
  error?: string
  helperText?: string
  disabled?: boolean
  rows?: number
  maxLength?: number
  showCharacterCount?: boolean
  required?: boolean
  containerStyle?: any
}

export const TextArea = forwardRef<TextInput, TextAreaProps>(({
  label,
  placeholder,
  error,
  helperText,
  disabled = false,
  rows = 4,
  maxLength,
  showCharacterCount = false,
  required = false,
  containerStyle,
  onFocus,
  onBlur,
  value,
  onChangeText,
  ...textInputProps
}, ref) => {
  const { isDark } = useTheme()
  const [isFocused, setIsFocused] = useState(false)
  const [text, setText] = useState(value || '')

  const handleFocus = (e: any) => {
    setIsFocused(true)
    onFocus?.(e)
  }

  const handleBlur = (e: any) => {
    setIsFocused(false)
    onBlur?.(e)
  }

  const handleChangeText = (newText: string) => {
    setText(newText)
    onChangeText?.(newText)
  }

  // Calculate height based on rows
  const minHeight = rows * 20 + 32 // Approximate line height + padding

  // Get border color based on state
  const getBorderColor = () => {
    if (error) return '$inputBorderColorError'
    if (isFocused) return '$inputBorderColorFocus'
    if (disabled) return '$inputBorderColor'
    return '$inputBorderColor'
  }

  // Get background color based on state
  const getBackgroundColor = () => {
    if (disabled) return '$inputBackgroundDisabled'
    if (isFocused) return '$inputBackgroundFocus'
    return '$inputBackground'
  }

  const characterCount = text.length
  const isOverLimit = maxLength ? characterCount > maxLength : false

  return (
    <View gap="$xs" style={containerStyle}>
      {/* Label */}
      {label && (
        <Text 
          fontSize="$labelMedium" 
          fontWeight="semibold" 
          color={error ? '$inputBorderColorError' : '$color'}
        >
          {label}
          {required && (
            <Text color="$inputBorderColorError"> *</Text>
          )}
        </Text>
      )}

      {/* TextArea Container */}
      <View
        backgroundColor={getBackgroundColor()}
        borderWidth="$inputBorderWidth"
        borderColor={getBorderColor()}
        borderRadius="$input"
        padding="$lg"
        opacity={disabled ? 0.6 : 1}
        minHeight={minHeight}
      >
        <TextInput
          ref={ref}
          placeholder={placeholder}
          placeholderTextColor={isDark ? '#6C7278' : '#A0A0A0'}
          editable={!disabled}
          multiline
          textAlignVertical="top"
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={text}
          onChangeText={handleChangeText}
          maxLength={maxLength}
          style={{
            flex: 1,
            fontSize: 14,
            color: isDark ? '#F8F9FA' : '#2C3E50',
            minHeight: minHeight - 32, // Subtract padding
          }}
          {...textInputProps}
        />
      </View>

      {/* Footer with helper text and character count */}
      <View flexDirection="row" justifyContent="space-between" alignItems="center">
        {/* Helper Text or Error */}
        <View flex={1}>
          {(error || helperText) && (
            <Text 
              fontSize="$labelSmall" 
              color={error ? '$inputBorderColorError' : '$colorSecondary'}
            >
              {error || helperText}
            </Text>
          )}
        </View>

        {/* Character Count */}
        {(showCharacterCount || maxLength) && (
          <Text 
            fontSize="$labelSmall" 
            color={isOverLimit ? '$inputBorderColorError' : '$colorSecondary'}
          >
            {characterCount}{maxLength && `/${maxLength}`}
          </Text>
        )}
      </View>
    </View>
  )
})

TextArea.displayName = 'TextArea'
