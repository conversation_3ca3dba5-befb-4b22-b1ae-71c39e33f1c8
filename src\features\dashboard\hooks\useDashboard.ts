import { useQuery, useQueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '../../../constants/api.constants';
import { dashboardService } from '../services/dashboard.service';

/**
 * Hook for active sessions data
 */
export const useActiveSessions = () => {
  return useQuery({
    queryKey: ['dashboard', 'active-sessions'],
    queryFn: async () => {
      try {
        const response = await dashboardService.getActiveSessions();
        if (!response.success) {
          throw new Error(response.message || 'Failed to fetch active sessions');
        }
        return response.data;
      } catch (error: any) {
        // Handle 404 gracefully - endpoint not implemented yet
        if (error.status === 404 || error.response?.status === 404) {
          console.warn('Active sessions endpoint not implemented, returning empty data');
          return [];
        }
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry 404 errors
      if (error.status === 404 || error.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook for pending appointments data
 */
export const usePendingAppointments = () => {
  return useQuery({
    queryKey: ['dashboard', 'pending-appointments'],
    queryFn: async () => {
      try {
        const response = await dashboardService.getPendingAppointments();
        if (!response.success) {
          throw new Error(response.message || 'Failed to fetch pending appointments');
        }
        return response.data;
      } catch (error: any) {
        // Handle 404 gracefully - endpoint not implemented yet
        if (error.status === 404 || error.response?.status === 404) {
          console.warn('Pending appointments endpoint not implemented, returning empty data');
          return [];
        }
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry 404 errors
      if (error.status === 404 || error.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook for today's appointments data
 */
export const useTodayAppointments = () => {
  const today = new Date();
  const startOfDay = today.toISOString().split('T')[0]; // YYYY-MM-DD format
  const endOfDay = today.toISOString().split('T')[0]; // YYYY-MM-DD format

  return useQuery({
    queryKey: ['dashboard', 'today-appointments', startOfDay],
    queryFn: async () => {
      try {
        const response = await dashboardService.getTodayAppointments(startOfDay, endOfDay);
        if (!response.success) {
          throw new Error(response.message || 'Failed to fetch today\'s appointments');
        }
        return response.data;
      } catch (error: any) {
        // Handle 404 gracefully - endpoint not implemented yet
        if (error.status === 404 || error.response?.status === 404) {
          console.warn('Today appointments endpoint not implemented, returning empty data');
          return [];
        }
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry 404 errors
      if (error.status === 404 || error.response?.status === 404) {
        return false;
      }
      return failureCount < 2; // Only retry once for validation errors
    },
  });
};

/**
 * Hook for profile completion data
 */
export const useProfileCompletion = () => {
  return useQuery({
    queryKey: ['dashboard', 'profile-completion'],
    queryFn: async () => {
      const response = await dashboardService.getProfileCompletion();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch profile completion');
      }
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 30 * 60 * 1000, // Refetch every 30 minutes
  });
};

/**
 * Hook for revenue analytics
 */
export const useRevenueAnalytics = (params: {
  startDate: string;
  endDate: string;
  groupBy?: 'day' | 'week' | 'month';
}) => {
  return useQuery({
    queryKey: [...QUERY_KEYS.REVENUE_ANALYTICS, params],
    queryFn: async () => {
      const response = await dashboardService.getRevenueAnalytics(params);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch revenue analytics');
      }
      return response.data;
    },
    enabled: !!(params.startDate && params.endDate),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for appointment analytics
 */
export const useAppointmentAnalytics = (params: {
  startDate: string;
  endDate: string;
  groupBy?: 'day' | 'week' | 'month';
}) => {
  return useQuery({
    queryKey: [...QUERY_KEYS.APPOINTMENT_ANALYTICS, params],
    queryFn: async () => {
      const response = await dashboardService.getAppointmentAnalytics(params);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch appointment analytics');
      }
      return response.data;
    },
    enabled: !!(params.startDate && params.endDate),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for customer analytics
 */
export const useCustomerAnalytics = (params: {
  startDate: string;
  endDate: string;
}) => {
  return useQuery({
    queryKey: [...QUERY_KEYS.CUSTOMER_ANALYTICS, params],
    queryFn: async () => {
      const response = await dashboardService.getCustomerAnalytics(params);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch customer analytics');
      }
      return response.data;
    },
    enabled: !!(params.startDate && params.endDate),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for performance metrics
 */
export const usePerformanceMetrics = () => {
  return useQuery({
    queryKey: QUERY_KEYS.PERFORMANCE_ANALYTICS,
    queryFn: async () => {
      const response = await dashboardService.getPerformanceMetrics();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch performance metrics');
      }
      return response.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook for refreshing dashboard data
 */
export const useDashboardRefresh = () => {
  const queryClient = useQueryClient();

  const refreshAll = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_OVERVIEW }),
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TODAY_SCHEDULE }),
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.QUICK_STATS }),
    ]);
  };

  const refreshOverview = () => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_OVERVIEW });
  };

  const refreshSchedule = () => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TODAY_SCHEDULE });
  };

  const refreshStats = () => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.QUICK_STATS });
  };

  return {
    refreshAll,
    refreshOverview,
    refreshSchedule,
    refreshStats,
  };
};
