{"root": "D:\\reactnative adscloud\\dalti_provider_2", "reactNativePath": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-picker/picker": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\@react-native-picker\\picker", "name": "@react-native-picker/picker", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\@react-native-picker\\picker\\android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-bottom-tabs": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-bottom-tabs", "name": "react-native-bottom-tabs", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-bottom-tabs\\android", "packageImportPath": "import com.rcttabview.RCTTabViewPackage;", "packageInstance": "new RCTTabViewPackage()", "buildTypes": [], "libraryName": "RNCTabView", "componentDescriptors": ["RNCTabViewComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-keychain": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-keychain", "name": "react-native-keychain", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-keychain\\android", "packageImportPath": "import com.oblador.keychain.KeychainPackage;", "packageInstance": "new KeychainPackage()", "buildTypes": [], "libraryName": "RNKeychainSpec", "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-keychain/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-mmkv": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-mmkv", "name": "react-native-mmkv", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-mmkv\\android", "packageImportPath": "import com.mrousavy.mmkv.MmkvPackage;", "packageInstance": "new MmkvPackage()", "buildTypes": [], "libraryName": "RNMmkvSpec", "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": "react-native-mmkv", "cxxModuleCMakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-mmkv/android/CMakeLists.txt", "cxxModuleHeaderName": "NativeMmkvModule"}}}, "react-native-reanimated": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor", "RNSBottomTabsComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-svg\\android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "D:/reactnative adscloud/dalti_provider_2/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "org.adscloud.dalti.provider", "sourceDir": "D:\\reactnative adscloud\\dalti_provider_2\\android"}}}