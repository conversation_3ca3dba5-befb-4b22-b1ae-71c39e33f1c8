import { Text, View } from '@tamagui/core'
import { useLocalSearchParams, useRouter } from 'expo-router'
import React, { useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { PasswordInput } from '../../../../components/ui/PasswordInput'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../../../../theme/themes'
import { AuthService } from '../services/auth.service'

export default function ResetPasswordScreen() {
  const router = useRouter()
  const params = useLocalSearchParams()
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme
  const insets = useSafeAreaInsets()
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)

  const token = params.token as string

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#257587',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#15424E',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  const handleBack = () => {
    router.back()
  }

  const validatePassword = (password: string): string | null => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  const handleResetPassword = async () => {
    if (!newPassword.trim()) {
      Alert.alert('Error', 'Please enter a new password')
      return
    }

    if (!confirmPassword.trim()) {
      Alert.alert('Error', 'Please confirm your password')
      return
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match')
      return
    }

    const passwordError = validatePassword(newPassword)
    if (passwordError) {
      Alert.alert('Error', passwordError)
      return
    }

    setLoading(true)

    try {
      const authService = AuthService.getInstance()
      const response = await authService.resetPassword(token, newPassword.trim())

      // Check for success - either response.success is true OR response has a message (indicating success)
      if (response.success || response.message) {
        Alert.alert(
          'Success',
          'Your password has been reset successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                router.push('/login')
              },
            },
          ]
        )
      } else {
        Alert.alert('Error', response.message || 'Failed to reset password')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Reset Password
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
              lineHeight={24}
            >
              Enter your new password below
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* New Password Input */}
              <PasswordInput
                label="New Password"
                placeholder="Enter new password"
                value={newPassword}
                onChangeText={setNewPassword}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
              />

              {/* Confirm Password Input */}
              <PasswordInput
                label="Confirm Password"
                placeholder="Confirm new password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
              />

              {/* Password Requirements */}
              <View gap="$xs" marginTop="$sm">
                <Text
                  fontSize="$bodySmall"
                  color={authColors.secondaryText}
                  fontWeight="semibold"
                >
                  Password Requirements:
                </Text>
                <Text fontSize="$bodySmall" color={authColors.secondaryText}>
                  • At least 8 characters long
                </Text>
                <Text fontSize="$bodySmall" color={authColors.secondaryText}>
                  • Contains uppercase and lowercase letters
                </Text>
                <Text fontSize="$bodySmall" color={authColors.secondaryText}>
                  • Contains at least one number
                </Text>
              </View>

              {/* Reset Password Button */}
              <Button
                title="Reset Password"
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                onPress={handleResetPassword}
                containerStyle={{ marginTop: 24 }}
              />

              {/* Back to Login */}
              <View alignItems="center" marginTop="$lg">
                <Pressable onPress={() => router.push('/login')}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Back to Login
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
