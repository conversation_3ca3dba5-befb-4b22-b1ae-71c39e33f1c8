{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1363,1453,1517,1580,1642,1710,1774,1838,1907,1963,2017,2140,2205,2267,2321,2392,2519,2603,2677,2774,2855,2939,3075,3152,3229,3345,3432,3511,3568,3623,3689,3765,3845,3916,3992,4059,4133,4203,4269,4371,4457,4527,4618,4708,4782,4855,4944,4995,5076,5148,5229,5315,5377,5441,5504,5573,5687,5793,5901,6003,6097,6187,6248,6307,6387,6471,6550", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,75,89,63,62,61,67,63,63,68,55,53,122,64,61,53,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,93,89,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1358,1448,1512,1575,1637,1705,1769,1833,1902,1958,2012,2135,2200,2262,2316,2387,2514,2598,2672,2769,2850,2934,3070,3147,3224,3340,3427,3506,3563,3618,3684,3760,3840,3911,3987,4054,4128,4198,4264,4366,4452,4522,4613,4703,4777,4850,4939,4990,5071,5143,5224,5310,5372,5436,5499,5568,5682,5788,5896,5998,6092,6182,6243,6302,6382,6466,6545,6620"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2994,3068,3140,3219,3301,4098,4195,4310,4694,4752,4817,6469,6533,6594,6670,6760,6824,6887,6949,7017,7081,7145,7214,7270,7324,7447,7512,7574,7628,7699,7826,7910,7984,8081,8162,8246,8382,8459,8536,8652,8739,8818,8875,8930,8996,9072,9152,9223,9299,9366,9440,9510,9576,9678,9764,9834,9925,10015,10089,10162,10251,10302,10383,10455,10536,10622,10684,10748,10811,10880,10994,11100,11208,11310,11404,11494,11555,11614,11775,11859,11938", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,75,89,63,62,61,67,63,63,68,55,53,122,64,61,53,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,93,89,60,58,79,83,78,74", "endOffsets": "315,3063,3135,3214,3296,3376,4190,4305,4387,4747,4812,4900,6528,6589,6665,6755,6819,6882,6944,7012,7076,7140,7209,7265,7319,7442,7507,7569,7623,7694,7821,7905,7979,8076,8157,8241,8377,8454,8531,8647,8734,8813,8870,8925,8991,9067,9147,9218,9294,9361,9435,9505,9571,9673,9759,9829,9920,10010,10084,10157,10246,10297,10378,10450,10531,10617,10679,10743,10806,10875,10989,11095,11203,11305,11399,11489,11550,11609,11689,11854,11933,12008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3476,3583,3680,3780,3883,3987,12013", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3471,3578,3675,3775,3878,3982,4093,12109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,253,369,500,631,756,882,1009,1108,1250,1392", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "159,248,364,495,626,751,877,1004,1103,1245,1387,1504"}, "to": {"startLines": "48,50,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4392,4605,5213,5329,5460,5591,5716,5842,5969,6068,6210,6352", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "4496,4689,5324,5455,5586,5711,5837,5964,6063,6205,6347,6464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "49,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4501,4905,5006,5112", "endColumns": "103,100,105,100", "endOffsets": "4600,5001,5107,5208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,11694", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,11770"}}]}]}