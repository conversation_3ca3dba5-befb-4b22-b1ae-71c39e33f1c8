{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1162,1239,1298,1361,1452,1521,1588,1670,1770,1833,1898,1959,2027,2089,2156,2225,2282,2340,2454,2514,2575,2632,2705,2828,2909,3001,3108,3206,3286,3434,3515,3596,3724,3813,3889,3942,3996,4062,4140,4220,4291,4373,4445,4519,4592,4662,4771,4862,4933,5023,5118,5192,5275,5368,5417,5498,5567,5653,5738,5800,5864,5927,5996,6105,6215,6312,6412,6504,6602,6659,6717,6797,6876,6951", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "endColumns": "12,77,75,83,91,82,100,118,63,76,58,62,90,68,66,81,99,62,64,60,67,61,66,68,56,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,91,97,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1157,1234,1293,1356,1447,1516,1583,1665,1765,1828,1893,1954,2022,2084,2151,2220,2277,2335,2449,2509,2570,2627,2700,2823,2904,2996,3103,3201,3281,3429,3510,3591,3719,3808,3884,3937,3991,4057,4135,4215,4286,4368,4440,4514,4587,4657,4766,4857,4928,5018,5113,5187,5270,5363,5412,5493,5562,5648,5733,5795,5859,5922,5991,6100,6210,6307,6407,6499,6597,6654,6712,6792,6871,6946,7022"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,53,58,59,60,75,78,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3238,3316,3392,3476,3568,4363,4464,4583,4647,5109,5168,5231,6987,7202,7336,7418,7518,7581,7646,7707,7775,7837,7904,7973,8030,8088,8202,8262,8323,8380,8453,8803,8884,8976,9083,9181,9261,9409,9490,9571,9699,9788,9864,9917,9971,10037,10115,10195,10266,10348,10420,10494,10567,10637,10746,10837,10908,10998,11093,11167,11250,11343,11392,11473,11542,11628,11713,11775,11839,11902,11971,12080,12190,12287,12387,12479,12577,12634,12692,13181,13260,13335", "endLines": "9,38,39,40,41,42,50,51,52,53,58,59,60,75,78,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,153,154,155", "endColumns": "12,77,75,83,91,82,100,118,63,76,58,62,90,68,66,81,99,62,64,60,67,61,66,68,56,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,91,97,56,57,79,78,74,75", "endOffsets": "510,3311,3387,3471,3563,3646,4459,4578,4642,4719,5163,5226,5317,7051,7264,7413,7513,7576,7641,7702,7770,7832,7899,7968,8025,8083,8197,8257,8318,8375,8448,8571,8879,8971,9078,9176,9256,9404,9485,9566,9694,9783,9859,9912,9966,10032,10110,10190,10261,10343,10415,10489,10562,10632,10741,10832,10903,10993,11088,11162,11245,11338,11387,11468,11537,11623,11708,11770,11834,11897,11966,12075,12185,12282,12382,12474,12572,12629,12687,12767,13255,13330,13406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,278,346,424,491,561,639,718,799,887,965,1045,1129,1203,1281,1358,1433,1512,1584,1668,1738,1824,1893", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,201,273,341,419,486,556,634,713,794,882,960,1040,1124,1198,1276,1353,1428,1507,1579,1663,1733,1819,1888,1966"}, "to": {"startLines": "37,54,74,76,77,79,97,98,99,148,149,150,151,156,157,158,159,160,161,162,163,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3169,4724,6915,7056,7124,7269,8576,8646,8724,12772,12853,12941,13019,13411,13495,13569,13647,13724,13799,13878,13950,14135,14205,14291,14360", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "3233,4801,6982,7119,7197,7331,8641,8719,8798,12848,12936,13014,13094,13490,13564,13642,13719,13794,13873,13945,14029,14200,14286,14355,14433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3651,3744,3846,3941,4044,4147,4249,14034", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "3739,3841,3936,4039,4142,4244,4358,14130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,258,376,496,639,784,906,1040,1146,1286,1433", "endColumns": "112,89,117,119,142,144,121,133,105,139,146,109", "endOffsets": "163,253,371,491,634,779,901,1035,1141,1281,1428,1538"}, "to": {"startLines": "55,57,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4806,5019,5630,5748,5868,6011,6156,6278,6412,6518,6658,6805", "endColumns": "112,89,117,119,142,144,121,133,105,139,146,109", "endOffsets": "4914,5104,5743,5863,6006,6151,6273,6407,6513,6653,6800,6910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "56,61,62,63", "startColumns": "4,4,4,4", "startOffsets": "4919,5322,5420,5528", "endColumns": "99,97,107,101", "endOffsets": "5014,5415,5523,5625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,13099", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,13176"}}]}]}