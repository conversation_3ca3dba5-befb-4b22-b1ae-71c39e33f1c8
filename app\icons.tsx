import { Text, View } from '@tamagui/core'
import React from 'react'

export default function IconsScreen() {
  return (
    <View flex={1} backgroundColor="#f5f5f5" padding="$lg">
      <Text fontSize="$headlineLarge" fontWeight="bold" color="#333">
        Icons Library Showcase
      </Text>
      <Text fontSize="$bodyMedium" color="#666" marginTop="$md">
        Working on icons library implementation...
      </Text>

      <View marginTop="$lg">
        <Text fontSize="$headlineMedium" fontWeight="bold" color="#333" marginBottom="$md">
          Status: Foundation Complete
        </Text>

        <View flexDirection="row" flexWrap="wrap" gap="$md">
          {['home', 'person', 'settings', 'search'].map((name) => (
            <View key={name} alignItems="center" padding="$sm" width="23%">
              <View
                padding="$md"
                borderRadius="$md"
                backgroundColor="#e0e0e0"
                marginBottom="$xs"
                alignItems="center"
                justifyContent="center"
                aspectRatio={1}
                borderWidth={1}
                borderColor="#ccc"
              >
                <Text fontSize="$bodySmall" color="#257587">
                  {name.slice(0, 4)}
                </Text>
              </View>
              <Text
                fontSize="$bodySmall"
                color="#666"
                textAlign="center"
                numberOfLines={2}
                fontWeight="500"
              >
                {name}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  )
}
