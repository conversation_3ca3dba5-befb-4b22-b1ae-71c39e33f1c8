{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3536,3641,3739,3838,3943,4045,12778", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3531,3636,3734,3833,3938,4040,4151,12874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "48,69,70,72,90,143,144", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4462,6738,6806,6949,8260,12629,12704", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4534,6801,6882,7012,8327,12699,12773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,257,372,498,621,752,871,1033,1136,1268,1403", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "164,252,367,493,616,747,866,1028,1131,1263,1398,1521"}, "to": {"startLines": "49,51,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4539,4757,5384,5499,5625,5748,5879,5998,6160,6263,6395,6530", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "4648,4840,5494,5620,5743,5874,5993,6155,6258,6390,6525,6648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "50,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4653,5067,5170,5281", "endColumns": "103,102,110,102", "endOffsets": "4752,5165,5276,5379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,12309", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,12387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1429,1516,1578,1642,1703,1770,1831,1902,1976,2032,2086,2208,2265,2325,2379,2460,2595,2679,2755,2845,2924,3009,3145,3220,3295,3438,3533,3613,3669,3722,3788,3862,3941,4012,4095,4166,4242,4318,4395,4501,4589,4669,4765,4861,4935,5013,5113,5164,5248,5317,5404,5495,5557,5621,5684,5755,5860,5966,6066,6169,6271,6370,6430,6487,6572,6655,6729", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,76,86,61,63,60,66,60,70,73,55,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,101,98,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1424,1511,1573,1637,1698,1765,1826,1897,1971,2027,2081,2203,2260,2320,2374,2455,2590,2674,2750,2840,2919,3004,3140,3215,3290,3433,3528,3608,3664,3717,3783,3857,3936,4007,4090,4161,4237,4313,4390,4496,4584,4664,4760,4856,4930,5008,5108,5159,5243,5312,5399,5490,5552,5616,5679,5750,5855,5961,6061,6164,6266,6365,6425,6482,6567,6650,6724,6804"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,52,53,54,68,71,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,3348,4156,4251,4381,4845,4906,4970,6653,6887,7017,7094,7181,7243,7307,7368,7435,7496,7567,7641,7697,7751,7873,7930,7990,8044,8125,8332,8416,8492,8582,8661,8746,8882,8957,9032,9175,9270,9350,9406,9459,9525,9599,9678,9749,9832,9903,9979,10055,10132,10238,10326,10406,10502,10598,10672,10750,10850,10901,10985,11054,11141,11232,11294,11358,11421,11492,11597,11703,11803,11906,12008,12107,12167,12224,12392,12475,12549", "endLines": "5,33,34,35,36,37,45,46,47,52,53,54,68,71,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,140,141,142", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,76,86,61,63,60,66,60,70,73,55,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,101,98,59,56,84,82,73,79", "endOffsets": "310,3095,3171,3251,3343,3431,4246,4376,4457,4901,4965,5062,6733,6944,7089,7176,7238,7302,7363,7430,7491,7562,7636,7692,7746,7868,7925,7985,8039,8120,8255,8411,8487,8577,8656,8741,8877,8952,9027,9170,9265,9345,9401,9454,9520,9594,9673,9744,9827,9898,9974,10050,10127,10233,10321,10401,10497,10593,10667,10745,10845,10896,10980,11049,11136,11227,11289,11353,11416,11487,11592,11698,11798,11901,12003,12102,12162,12219,12304,12470,12544,12624"}}]}]}