import { View } from '@tamagui/core'
import React from 'react'
import { AppBar } from './AppBar'
import { AppIcons } from './Icons'

/**
 * Example usage of the AppBar component
 * This file demonstrates various configurations of the AppBar
 */

export const AppBarExamples: React.FC = () => {
  return (
    <View gap="$lg">
      {/* Example 1: Basic AppBar with title and back button */}
      <AppBar
        title="Dashboard"
        startIcon={{
          icon: <AppIcons.back size={20} color="#666" />,
          onPress: () => console.log('Back pressed'),
        }}
      />

      {/* Example 2: AppBar with title and multiple end icons */}
      <AppBar
        title="Settings"
        startIcon={{
          icon: <AppIcons.menu size={20} color="#666" />,
          onPress: () => console.log('Menu pressed'),
        }}
        endIcons={[
          {
            icon: <AppIcons.search size={20} color="#666" />,
            onPress: () => console.log('Search pressed'),
          },
          {
            icon: <AppIcons.settings size={20} color="#666" />,
            onPress: () => console.log('Settings pressed'),
          },
        ]}
      />

      {/* Example 3: AppBar with only end icons (no title) */}
      <AppBar
        endIcons={[
          {
            icon: <AppIcons.notification size={20} color="#666" />,
            onPress: () => console.log('Notifications pressed'),
          },
          {
            icon: <AppIcons.person size={20} color="#666" />,
            onPress: () => console.log('Profile pressed'),
          },
        ]}
      />

      {/* Example 4: AppBar with custom colors */}
      <AppBar
        title="Custom Theme"
        backgroundColor="#257587"
        titleColor="#FFFFFF"
        startIcon={{
          icon: <AppIcons.back size={20} color="#FFFFFF" />,
          onPress: () => console.log('Back pressed'),
        }}
        endIcon={{
          icon: <AppIcons.settings size={20} color="#FFFFFF" />,
          onPress: () => console.log('Settings pressed'),
        }}
      />

      {/* Example 5: AppBar with disabled icon */}
      <AppBar
        title="With Disabled Icon"
        startIcon={{
          icon: <AppIcons.back size={20} color="#666" />,
          onPress: () => console.log('Back pressed'),
        }}
        endIcon={{
          icon: <AppIcons.settings size={20} color="#666" />,
          onPress: () => console.log('Settings pressed'),
          disabled: true,
        }}
      />

      {/* Example 6: AppBar with non-clickable icons */}
      <AppBar
        title="Non-clickable Icons"
        startIcon={{
          icon: <AppIcons.home size={20} color="#666" />,
          // No onPress = non-clickable
        }}
        endIcon={{
          icon: <AppIcons.star size={20} color="#666" />,
          // No onPress = non-clickable
        }}
      />
    </View>
  )
}

/**
 * Usage in a screen component:
 * 
 * import { AppBar } from '../../components/ui/AppBar'
 * import { AppIcons } from '../../components/ui/Icons'
 * 
 * const MyScreen = () => {
 *   const handleBack = () => {
 *     router.back()
 *   }
 * 
 *   const handleSearch = () => {
 *     // Handle search
 *   }
 * 
 *   const handleSettings = () => {
 *     // Handle settings
 *   }
 * 
 *   return (
 *     <View flex={1}>
 *       <AppBar
 *         title="My Screen"
 *         startIcon={{
 *           icon: <AppIcons.back size={20} color="#666" />,
 *           onPress: handleBack,
 *         }}
 *         endIcons={[
 *           {
 *             icon: <AppIcons.search size={20} color="#666" />,
 *             onPress: handleSearch,
 *           },
 *           {
 *             icon: <AppIcons.settings size={20} color="#666" />,
 *             onPress: handleSettings,
 *           },
 *         ]}
 *       />
 *
 *       Screen content
 *       <View flex={1}>
 *         Your screen content here
 *       </View>
 *     </View>
 *   )
 * }
 */
