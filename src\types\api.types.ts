// API Types and Interfaces

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
}

// Authentication Types
export interface LoginRequest {
  identifier: string; // email or phone
  password: string;
}

export interface RegisterRequest {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  isProviderRegistration: true;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface OTPVerificationRequest {
  otp: string;
  identifier: string;
  password: string;
  firstName: string;
  lastName: string;
  providerCategoryId: number;
  businessName: string;
  phone: string;
  email: string;
}

export interface PasswordResetOTPRequest {
  email: string;
}

export interface PasswordResetOTPVerificationRequest {
  otp: string;
  email: string;
}

export interface ResetPasswordRequest {
  resetToken: string;
  newPassword: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface Provider {
  id: number;
  userId: string;
  providerCategoryId: number;
  title: string;
  phone: string;
  isSetupComplete: boolean;
  category?: {
    id: number;
    title: string;
    parentId: number | null;
  };
}

export interface AuthResponse {
  sessionId: string;
  user: AuthUser;
  provider: Provider;
}

export interface ProviderCategory {
  id: number;
  title: string;
  parentId: number | null;
}

// Business Types
export interface Location {
  id: number;
  name: string;
  address: string;
  city: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  isMobileHidden: boolean;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
}

export interface CreateLocationRequest {
  name: string;
  address: string;
  city: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
}

export interface Service {
  id: number;
  title: string;
  description?: string;
  duration: number;
  price?: number;
  categoryId?: number;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  pointsRequirements: number;
  isActive?: boolean;
}

export interface CreateServiceRequest {
  title: string;
  description?: string;
  duration: number;
  price?: number;
  categoryId?: number;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
}

export interface Queue {
  id: number;
  title: string;
  sProvidingPlaceId: number;
  serviceIds: number[];
  isActive: boolean;
  maxCapacity: number;
  currentCount: number;
}

export interface CreateQueueRequest {
  title: string;
  sProvidingPlaceId: number;
  serviceIds: number[];
  maxCapacity: number;
  isActive: boolean;
}

// Customer Types
export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  nationalId?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  notes?: string;
  totalAppointments: number;
  lastAppointment?: string;
}

export interface CreateCustomerRequest {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  nationalId?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  notes?: string;
}

// Appointment Types
export interface Appointment {
  id: number;
  customerUserId: string;
  serviceId: number;
  placeId: number;
  queueId: number;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  notes?: string;
  customer?: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
  };
  service?: {
    title: string;
    duration: number;
  };
}

export interface CreateAppointmentRequest {
  customerUserId: string;
  serviceId: number;
  placeId: number;
  queueId: number;
  startTime: string;
  endTime: string;
  notes?: string;
}

// Dashboard Types
export interface DashboardOverview {
  todayRevenue: number;
  todayAppointments: number;
  activeQueues: number;
  customerSatisfaction: number;
  weeklyTrends: {
    revenue: number[];
    appointments: number[];
  };
  monthlyStats: {
    totalRevenue: number;
    totalAppointments: number;
    newCustomers: number;
    repeatCustomers: number;
  };
}

export interface TodaySchedule {
  nextAppointment?: {
    id: number;
    customerName: string;
    serviceName: string;
    startTime: string;
    location: string;
  };
  queueStatuses: Array<{
    queueId: number;
    queueName: string;
    currentCount: number;
    maxCapacity: number;
    estimatedWaitTime: number;
  }>;
  todayHours: {
    openTime: string;
    closeTime: string;
    isOpen: boolean;
  };
  totalAppointmentsToday: number;
  completedAppointments: number;
  upcomingAppointments: number;
}

export interface QuickStats {
  todayRevenue: number;
  todayAppointments: number;
  waitingCustomers: number;
  activeQueues: number;
  unreadMessages: number;
  pendingReschedules: number;
  customerSatisfactionToday: number;
  averageWaitTime: number;
}

// Messaging Types
export interface Conversation {
  id: number;
  customerId: string;
  customerName: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isActive: boolean;
}

export interface Message {
  id: number;
  conversationId: number;
  senderId: string;
  senderType: 'provider' | 'customer';
  content: string;
  timestamp: string;
  isRead: boolean;
  messageType: 'text' | 'image' | 'file';
}

export interface SendMessageRequest {
  content: string;
  messageType: 'text' | 'image' | 'file';
}

// Notification Types
export interface Notification {
  id: number;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  timestamp: string;
  data?: Record<string, any>;
}
