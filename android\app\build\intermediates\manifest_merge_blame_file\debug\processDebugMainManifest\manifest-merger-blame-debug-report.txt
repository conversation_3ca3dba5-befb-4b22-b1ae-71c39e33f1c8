1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.adscloud.dalti.provider"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:4:3-75
11-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:2:3-64
12-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:5:3-63
16-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:6:3-78
17-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:7-58
21-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:7-67
23-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:7-37
25-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27        <intent>
27-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
28            <action android:name="org.chromium.intent.action.PAY" />
28-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
28-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
29        </intent>
30        <intent>
30-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
31            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
31-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
31-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
32        </intent>
33        <intent>
33-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
34            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
34-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
34-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
35        </intent>
36
37        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
37-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
37-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
38        <intent>
38-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
39            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
39-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
39-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
40        </intent>
41        <intent>
41-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
42
43            <!-- Required for opening tabs if targeting API 30 -->
44            <action android:name="android.support.customtabs.action.CustomTabsService" />
44-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
44-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
45        </intent>
46    </queries>
47
48    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
48-->[:react-native-keychain] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-72
48-->[:react-native-keychain] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-69
49    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
49-->[:react-native-keychain] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-74
49-->[:react-native-keychain] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-keychain\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-71
50    <!--
51  Allows Glide to monitor connectivity status and restart failed requests if users go from a
52  a disconnected to a connected network state.
53    -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:5-79
54-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:22-76
55
56    <permission
56-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
57        android:name="org.adscloud.dalti.provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="org.adscloud.dalti.provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:3-31:17
63        android:name="org.adscloud.dalti.provider.MainApplication"
63-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:16-47
64        android:allowBackup="true"
64-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:162-188
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79bf916755d3ffa67a1186a4c7c4c642\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="false"
68        android:icon="@mipmap/ic_launcher"
68-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:81-115
69        android:label="@string/app_name"
69-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:48-80
70        android:roundIcon="@mipmap/ic_launcher_round"
70-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:116-161
71        android:supportsRtl="true"
71-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:221-247
72        android:theme="@style/AppTheme"
72-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:14:189-220
73        android:usesCleartextTraffic="true" >
73-->D:\reactnative adscloud\dalti_provider_2\android\app\src\debug\AndroidManifest.xml:6:18-53
74        <meta-data
74-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:15:5-83
75            android:name="expo.modules.updates.ENABLED"
75-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:15:16-59
76            android:value="false" />
76-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:15:60-81
77        <meta-data
77-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:16:5-105
78            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
78-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:16:16-80
79            android:value="ALWAYS" />
79-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:16:81-103
80        <meta-data
80-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:17:5-99
81            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
81-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:17:16-79
82            android:value="0" />
82-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:17:80-97
83
84        <activity
84-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:5-30:16
85            android:name="org.adscloud.dalti.provider.MainActivity"
85-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:15-43
86            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
86-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:44-134
87            android:exported="true"
87-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:256-279
88            android:launchMode="singleTask"
88-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:135-166
89            android:screenOrientation="portrait"
89-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:280-316
90            android:theme="@style/Theme.App.SplashScreen"
90-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:210-255
91            android:windowSoftInputMode="adjustResize" >
91-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:18:167-209
92            <intent-filter>
92-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:19:7-22:23
93                <action android:name="android.intent.action.MAIN" />
93-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:20:9-60
93-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:20:17-58
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:21:9-68
95-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:21:19-66
96            </intent-filter>
97            <intent-filter>
97-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:23:7-29:23
98                <action android:name="android.intent.action.VIEW" />
98-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:7-58
98-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:15-56
99
100                <category android:name="android.intent.category.DEFAULT" />
100-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:9-67
100-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:19-65
101                <category android:name="android.intent.category.BROWSABLE" />
101-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:7-67
101-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:17-65
102
103                <data android:scheme="daltiprovider2" />
103-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:7-37
103-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:13-35
104                <data android:scheme="exp+daltiprovider2" />
104-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:7-37
104-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:13-35
105            </intent-filter>
106        </activity>
107
108        <provider
108-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
109            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
109-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
110            android:authorities="org.adscloud.dalti.provider.fileprovider"
110-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
111            android:exported="false"
111-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
112            android:grantUriPermissions="true" >
112-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
113            <meta-data
113-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
114                android:name="android.support.FILE_PROVIDER_PATHS"
114-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
115                android:resource="@xml/file_provider_paths" />
115-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
116        </provider>
117
118        <activity
118-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
119            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
119-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
120            android:exported="true"
120-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
121            android:launchMode="singleTask"
121-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
122            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
122-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
123            <intent-filter>
123-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
124                <action android:name="android.intent.action.VIEW" />
124-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:7-58
124-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:15-56
125
126                <category android:name="android.intent.category.DEFAULT" />
126-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:9-67
126-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:19-65
127                <category android:name="android.intent.category.BROWSABLE" />
127-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:7-67
127-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:17-65
128
129                <data android:scheme="expo-dev-launcher" />
129-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:7-37
129-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:13-35
130            </intent-filter>
131        </activity>
132        <activity
132-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
133            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
133-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
134            android:screenOrientation="portrait"
134-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
135            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
135-->[:expo-dev-launcher] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
136        <activity
136-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
137            android:name="expo.modules.devmenu.DevMenuActivity"
137-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
138            android:exported="true"
138-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
139            android:launchMode="singleTask"
139-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
140            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
140-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
141            <intent-filter>
141-->[:expo-dev-menu] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
142                <action android:name="android.intent.action.VIEW" />
142-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:7-58
142-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:9:15-56
143
144                <category android:name="android.intent.category.DEFAULT" />
144-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:9-67
144-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:25:19-65
145                <category android:name="android.intent.category.BROWSABLE" />
145-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:7-67
145-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:10:17-65
146
147                <data android:scheme="expo-dev-menu" />
147-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:7-37
147-->D:\reactnative adscloud\dalti_provider_2\android\app\src\main\AndroidManifest.xml:11:13-35
148            </intent-filter>
149        </activity>
150
151        <meta-data
151-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
152            android:name="org.unimodules.core.AppLoader#react-native-headless"
152-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
153            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
153-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
154        <meta-data
154-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
155            android:name="com.facebook.soloader.enabled"
155-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
156            android:value="true" />
156-->[:expo-modules-core] D:\reactnative adscloud\dalti_provider_2\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
157
158        <activity
158-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
159            android:name="com.facebook.react.devsupport.DevSettingsActivity"
159-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
160            android:exported="false" />
160-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
161
162        <provider
162-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
163            android:name="expo.modules.filesystem.FileSystemFileProvider"
163-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
164            android:authorities="org.adscloud.dalti.provider.FileSystemFileProvider"
164-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
165            android:exported="false"
165-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
166            android:grantUriPermissions="true" >
166-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
167            <meta-data
167-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
168                android:name="android.support.FILE_PROVIDER_PATHS"
168-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
169                android:resource="@xml/file_system_provider_paths" />
169-->[:react-native-webview] D:\reactnative adscloud\dalti_provider_2\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
170        </provider>
171
172        <meta-data
172-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
173            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
173-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
174            android:value="GlideModule" />
174-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
175
176        <provider
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
177            android:name="androidx.startup.InitializationProvider"
177-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
178            android:authorities="org.adscloud.dalti.provider.androidx-startup"
178-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
179            android:exported="false" >
179-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
180            <meta-data
180-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
181                android:name="androidx.emoji2.text.EmojiCompatInitializer"
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
182                android:value="androidx.startup" />
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
183            <meta-data
183-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\541598e8955756eef00aac9df9aeff7e\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
184                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
184-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\541598e8955756eef00aac9df9aeff7e\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
185                android:value="androidx.startup" />
185-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\541598e8955756eef00aac9df9aeff7e\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
186            <meta-data
186-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
187-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
188                android:value="androidx.startup" />
188-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
189        </provider>
190
191        <receiver
191-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
192            android:name="androidx.profileinstaller.ProfileInstallReceiver"
192-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
193            android:directBootAware="false"
193-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
194            android:enabled="true"
194-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
195            android:exported="true"
195-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
196            android:permission="android.permission.DUMP" >
196-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
198                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
198-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
198-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
201                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
201-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
204                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
204-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
207                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
207-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
207-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\331e87d2a85d7968bb31c428892d884f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
208            </intent-filter>
209        </receiver>
210    </application>
211
212</manifest>
