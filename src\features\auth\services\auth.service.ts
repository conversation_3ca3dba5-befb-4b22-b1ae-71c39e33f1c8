import { API_ENDPOINTS } from '../../../constants/api.constants';
import { apiClient } from '../../../services/api/api.client';
import {
    ApiResponse,
    AuthResponse,
    LoginRequest,
    OTPVerificationRequest,
    PasswordResetOTPVerificationRequest,
    ProviderCategory,
    RegisterRequest
} from '../../../types/api.types';

export class AuthService {
  private static instance: AuthService;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Login with email/phone and password
   */
  async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, data);

      // According to API spec, this endpoint returns direct AuthResponse, not wrapped in ApiResponse
      if (response && typeof response === 'object' && 'sessionId' in response) {
        return {
          success: true,
          data: response as AuthResponse,
        };
      }

      // If it's already wrapped in ApiResponse format
      return response;
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Login failed',
      };
    }
  }

  /**
   * Request email OTP for provider registration
   */
  async requestEmailOTP(data: RegisterRequest): Promise<ApiResponse<void>> {
    return await apiClient.post<void>(API_ENDPOINTS.AUTH.REQUEST_EMAIL_OTP, data);
  }

  /**
   * Verify OTP and complete provider registration
   */
  async verifyOTPAndRegister(data: OTPVerificationRequest): Promise<ApiResponse<AuthResponse>> {
    return await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.VERIFY_OTP_REGISTER, data);
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<{ sessionId: string }>> {
    return await apiClient.post<{ sessionId: string }>(API_ENDPOINTS.AUTH.REFRESH_TOKEN, {
      refreshToken,
    });
  }

  /**
   * Logout user
   */
  async logout(): Promise<ApiResponse<void>> {
    return await apiClient.post<void>(API_ENDPOINTS.AUTH.LOGOUT);
  }

  /**
   * Request password reset OTP - Step 1 of password reset workflow
   */
  async requestPasswordResetOTP(email: string): Promise<ApiResponse<void>> {
    return await apiClient.post<void>(API_ENDPOINTS.AUTH.REQUEST_PASSWORD_RESET_OTP, { email });
  }

  /**
   * Legacy method for backward compatibility
   */
  async forgotPassword(email: string): Promise<ApiResponse<void>> {
    return this.requestPasswordResetOTP(email);
  }

  /**
   * Verify OTP for password reset - Step 2 of password reset workflow
   */
  async verifyPasswordResetOTP(data: PasswordResetOTPVerificationRequest): Promise<ApiResponse<{ resetToken: string }>> {
    return await apiClient.post<{ resetToken: string }>(API_ENDPOINTS.AUTH.VERIFY_PASSWORD_RESET_OTP, data);
  }

  /**
   * Reset password with token - Step 3 of password reset workflow
   */
  async resetPassword(resetToken: string, newPassword: string): Promise<ApiResponse<void>> {
    return await apiClient.post<void>(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
      resetToken,
      newPassword,
    });
  }

  /**
   * Get provider categories for registration
   */
  async getProviderCategories(): Promise<ApiResponse<ProviderCategory[]>> {
    try {
      const response = await apiClient.get<ProviderCategory[]>(API_ENDPOINTS.AUTH.PROVIDER_CATEGORIES);

      // According to API spec, this endpoint returns a direct array, not wrapped in ApiResponse
      if (Array.isArray(response)) {
        return {
          success: true,
          data: response as ProviderCategory[],
        };
      }

      // If it's already wrapped in ApiResponse format
      return response;
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Failed to fetch categories',
      };
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format (basic validation)
   */
  validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if identifier is email or phone
   */
  getIdentifierType(identifier: string): 'email' | 'phone' {
    return this.validateEmail(identifier) ? 'email' : 'phone';
  }

  /**
   * Format phone number for API
   */
  formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Add + if not present and starts with country code
    if (!cleaned.startsWith('+') && cleaned.length > 10) {
      return `+${cleaned}`;
    }
    
    return cleaned;
  }

  /**
   * Generate OTP (for testing purposes)
   */
  generateTestOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Validate OTP format
   */
  validateOTP(otp: string): boolean {
    return /^\d{6}$/.test(otp);
  }

  /**
   * Check if user is authenticated (has valid token)
   */
  async checkAuthStatus(): Promise<boolean> {
    try {
      // This would typically be a lightweight endpoint to verify token
      const response = await apiClient.get('/auth/me');
      return response.success;
    } catch {
      return false;
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<ApiResponse<AuthResponse>> {
    return await apiClient.get<AuthResponse>('/auth/me');
  }

  /**
   * Update user profile
   */
  async updateProfile(data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  }): Promise<ApiResponse<void>> {
    return await apiClient.put<void>('/auth/profile', data);
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    return await apiClient.put<void>('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  /**
   * Delete account
   */
  async deleteAccount(password: string): Promise<ApiResponse<void>> {
    return await apiClient.delete<void>('/auth/account', {
      data: { password },
    });
  }

  /**
   * Resend OTP
   */
  async resendOTP(identifier: string): Promise<ApiResponse<void>> {
    return await apiClient.post<void>('/auth/otp/resend', { identifier });
  }

  /**
   * Check if email is available
   */
  async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return await apiClient.post<{ available: boolean }>('/auth/check-email', { email });
  }

  /**
   * Check if phone is available
   */
  async checkPhoneAvailability(phone: string): Promise<ApiResponse<{ available: boolean }>> {
    return await apiClient.post<{ available: boolean }>('/auth/check-phone', { phone });
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
