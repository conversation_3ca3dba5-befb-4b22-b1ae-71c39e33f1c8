import { API_ENDPOINTS } from '../../../constants/api.constants';
import { apiClient } from '../../../services/api/api.client';
import {
    ApiResponse,
} from '../../../types/api.types';

export class DashboardService {
  private static instance: DashboardService;

  public static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  /**
   * Get active service sessions
   */
  async getActiveSessions(): Promise<ApiResponse<any>> {
    return await apiClient.get<any>(API_ENDPOINTS.PROVIDER.ACTIVE_SESSIONS);
  }

  /**
   * Get pending appointments
   */
  async getPendingAppointments(): Promise<ApiResponse<any>> {
    return await apiClient.get<any>(API_ENDPOINTS.PROVIDER.PENDING_APPOINTMENTS);
  }

  /**
   * Get today's appointments
   */
  async getTodayAppointments(startDate: string, endDate: string): Promise<ApiResponse<any>> {
    return await apiClient.get<any>(API_ENDPOINTS.PROVIDER.TODAY_APPOINTMENTS, {
      params: { startDate, endDate }
    });
  }

  /**
   * Get profile completion status
   */
  async getProfileCompletion(): Promise<ApiResponse<any>> {
    return await apiClient.get<any>(API_ENDPOINTS.PROVIDER.PROFILE_COMPLETION);
  }

  /**
   * Get revenue analytics for a specific period
   */
  async getRevenueAnalytics(params: {
    startDate: string;
    endDate: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<ApiResponse<any>> {
    return await apiClient.get('/provider/analytics/revenue', { params });
  }

  /**
   * Get appointment analytics
   */
  async getAppointmentAnalytics(params: {
    startDate: string;
    endDate: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<ApiResponse<any>> {
    return await apiClient.get('/provider/analytics/appointments', { params });
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(params: {
    startDate: string;
    endDate: string;
  }): Promise<ApiResponse<any>> {
    return await apiClient.get('/provider/analytics/customers', { params });
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<ApiResponse<any>> {
    return await apiClient.get('/provider/analytics/performance');
  }
}

// Export singleton instance
export const dashboardService = DashboardService.getInstance();
