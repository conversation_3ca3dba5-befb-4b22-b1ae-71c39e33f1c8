{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "34,50,70,72,73,75,93,94,95,144,145,146,147,152,153,154,155,156,157,158,159,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3017,4489,6610,6750,6819,6963,8196,8264,8342,12188,12270,12349,12420,12813,12893,12966,13046,13124,13199,13271,13343,13531,13602,13681,13750", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3081,4562,6675,6814,6895,7026,8259,8337,8415,12265,12344,12415,12493,12888,12961,13041,13119,13194,13266,13338,13425,13597,13676,13745,13820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "40,41,42,43,44,45,46,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3506,3600,3702,3799,3896,3997,4097,13430", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3595,3697,3794,3891,3992,4092,4198,13526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,12498", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,12575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1439,1531,1594,1654,1713,1776,1837,1901,1969,2023,2077,2179,2236,2295,2349,2417,2528,2609,2684,2771,2851,2933,3065,3136,3209,3333,3421,3497,3550,3604,3670,3743,3819,3890,3968,4038,4113,4195,4263,4364,4449,4519,4609,4700,4774,4847,4936,4987,5068,5135,5217,5302,5364,5428,5491,5559,5653,5748,5838,5935,6017,6106,6163,6221,6296,6378,6453", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,75,91,62,59,58,62,60,63,67,53,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,81,88,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1434,1526,1589,1649,1708,1771,1832,1896,1964,2018,2072,2174,2231,2290,2344,2412,2523,2604,2679,2766,2846,2928,3060,3131,3204,3328,3416,3492,3545,3599,3665,3738,3814,3885,3963,4033,4108,4190,4258,4359,4444,4514,4604,4695,4769,4842,4931,4982,5063,5130,5212,5297,5359,5423,5486,5554,5648,5743,5833,5930,6012,6101,6158,6216,6291,6373,6448,6524"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,54,55,56,71,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3086,3163,3238,3315,3415,4203,4296,4409,4855,4915,4980,6680,6900,7031,7107,7199,7262,7322,7381,7444,7505,7569,7637,7691,7745,7847,7904,7963,8017,8085,8420,8501,8576,8663,8743,8825,8957,9028,9101,9225,9313,9389,9442,9496,9562,9635,9711,9782,9860,9930,10005,10087,10155,10256,10341,10411,10501,10592,10666,10739,10828,10879,10960,11027,11109,11194,11256,11320,11383,11451,11545,11640,11730,11827,11909,11998,12055,12113,12580,12662,12737", "endLines": "6,35,36,37,38,39,47,48,49,54,55,56,71,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149,150,151", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,75,91,62,59,58,62,60,63,67,53,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,81,88,56,57,74,81,74,75", "endOffsets": "356,3158,3233,3310,3410,3501,4291,4404,4484,4910,4975,5063,6745,6958,7102,7194,7257,7317,7376,7439,7500,7564,7632,7686,7740,7842,7899,7958,8012,8080,8191,8496,8571,8658,8738,8820,8952,9023,9096,9220,9308,9384,9437,9491,9557,9630,9706,9777,9855,9925,10000,10082,10150,10251,10336,10406,10496,10587,10661,10734,10823,10874,10955,11022,11104,11189,11251,11315,11378,11446,11540,11635,11725,11822,11904,11993,12050,12108,12183,12657,12732,12808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,252,370,499,622,753,867,994,1088,1231,1373", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "156,247,365,494,617,748,862,989,1083,1226,1368,1481"}, "to": {"startLines": "51,53,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4567,4764,5376,5494,5623,5746,5877,5991,6118,6212,6355,6497", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "4668,4850,5489,5618,5741,5872,5986,6113,6207,6350,6492,6605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "52,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4673,5068,5168,5274", "endColumns": "90,99,105,101", "endOffsets": "4759,5163,5269,5371"}}]}]}