import { Text, View } from '@tamagui/core'
import { useRouter } from 'expo-router'
import React, { useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { Input } from '../../../../components/ui/Input'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../../../../theme/themes'
import { AuthService } from '../services/auth.service'

export default function ForgotPasswordScreen() {
  const router = useRouter()
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme
  const insets = useSafeAreaInsets()
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#257587',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#15424E',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  const handleBack = () => {
    router.back()
  }

  const handleSendResetCode = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address')
      return
    }

    setLoading(true)

    try {
      const authService = AuthService.getInstance()
      const response = await authService.forgotPassword(email.trim())

      // Check for success - either response.success is true OR response has a message (indicating success)
      if (response.success || response.message) {
        // Navigate to OTP verification screen with email parameter
        router.push({
          pathname: '/password-reset-otp',
          params: { email: email.trim() }
        })
      } else {
        Alert.alert('Error', response.message || 'Failed to send reset code')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send reset code')
    } finally {
      setLoading(false)
    }
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Reset Password
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
              lineHeight={24}
            >
              Enter your email address and we&apos;ll send you a verification code to reset your password
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Enter your email address"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                variant="outlined"
                leftIcon={<AppIcons.mail size={20} color={authColors.inputIcons} />}
              />

              {/* Send Reset Code Button */}
              <Button
                title="Send Reset Code"
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                onPress={handleSendResetCode}
                containerStyle={{ marginTop: 16 }}
              />

              {/* Back to Login */}
              <View alignItems="center" marginTop="$xl">
                <Pressable onPress={handleBackToLogin}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Back to Login
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
