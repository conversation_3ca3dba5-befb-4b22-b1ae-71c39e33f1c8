import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, { Path } from 'react-native-svg';
import { Text, View } from 'tamagui';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const ProfessionalTabBar: React.FC<BottomTabBarProps> = ({ 
  state, 
  descriptors, 
  navigation 
}) => {
  const { isDark } = useTheme();
  const insets = useSafeAreaInsets();

  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const centerButtonBg = isDark ? '#257587' : '#15424E';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  const handleCenterPress = () => {
    console.log('QR Code scanner pressed');
    // TODO: Navigate to QR scanner or open modal
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: 'transparent', // Make background transparent
          paddingBottom: insets.bottom,
          width: '100%', // Ensure full width
          marginHorizontal: 0, // Remove any horizontal margins
          paddingHorizontal: 0, // Remove any horizontal padding
        },
      ]}
    >
      {/* Curved SVG Background - Full replacement */}
      <Svg
        width="100%"
        height={90}
        style={styles.svgBackground}
        viewBox="0 0 100 90"
        preserveAspectRatio="none"
      >
        {/* Background rectangle to cover any default tab bar */}
        <Path
          d="M0,0 L100,0 L100,90 L0,90 Z"
          fill={tabBarBg}
        />
        {/* Curved design on top */}
        <Path
          d="M0,30 L37,30 Q40,30 41,40 L44,60 Q50,80 56,60 L59,40 Q60,30 63,30 L100,30 L100,90 L0,90 Z"
          fill={tabBarBg}
          stroke={borderColor}
          strokeWidth={1}
        />
      </Svg>

      {/* Tab Bar Content */}
      <View style={styles.tabBarContent}>
        {/* Left tabs */}
        <View style={styles.tabSection}>
          {state.routes.slice(0, 2).map((route, index) => {
            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <Pressable key={route.key} onPress={onPress} style={styles.tab}>
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={isFocused ? 22 : 20}
                />
                <Text
                  fontSize={isFocused ? 12 : 11}
                  color={isFocused ? activeColor : inactiveColor}
                  fontWeight={isFocused ? '600' : '400'}
                  marginTop={2}
                >
                  {getTabLabel(route.name)}
                </Text>
              </Pressable>
            );
          })}
        </View>

        {/* Center button */}
        <View style={styles.centerSection}>
          <Pressable
            onPress={handleCenterPress}
            style={[
              styles.centerButton,
              { backgroundColor: centerButtonBg }
            ]}
          >
            <AppIcons.qrCode size={26} color="#FFFFFF" />
          </Pressable>
        </View>

        {/* Right tabs */}
        <View style={styles.tabSection}>
          {state.routes.slice(2, 4).map((route, index) => {
            const actualIndex = index + 2;
            const isFocused = state.index === actualIndex;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <Pressable key={route.key} onPress={onPress} style={styles.tab}>
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={isFocused ? 22 : 20}
                />
                <Text
                  fontSize={isFocused ? 12 : 11}
                  color={isFocused ? activeColor : inactiveColor}
                  fontWeight={isFocused ? '600' : '400'}
                  marginTop={2}
                >
                  {getTabLabel(route.name)}
                </Text>
              </Pressable>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 1000,
    zIndex: 1000,
  },
  svgBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  tabBarContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 80,
    paddingHorizontal: 20, // Increase padding for better spacing
    paddingTop: 30,
    zIndex: 2,
    width: '100%', // Ensure full width
  },
  tabSection: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 10,
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    minHeight: 50,
  },
  centerSection: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    marginBottom: 20,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
