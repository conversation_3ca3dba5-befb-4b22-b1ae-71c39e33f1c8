{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1446,1533,1597,1663,1723,1792,1853,1928,2005,2067,2121,2236,2295,2355,2409,2481,2611,2699,2778,2876,2964,3048,3186,3264,3340,3479,3573,3653,3709,3763,3829,3902,3980,4051,4135,4208,4286,4359,4434,4544,4634,4709,4803,4901,4975,5052,5152,5205,5289,5357,5446,5535,5597,5662,5725,5795,5902,6002,6102,6198,6288,6374,6434,6492,6572,6662,6737", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,78,86,63,65,59,68,60,74,76,61,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,89,85,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1441,1528,1592,1658,1718,1787,1848,1923,2000,2062,2116,2231,2290,2350,2404,2476,2606,2694,2773,2871,2959,3043,3181,3259,3335,3474,3568,3648,3704,3758,3824,3897,3975,4046,4130,4203,4281,4354,4429,4539,4629,4704,4798,4896,4970,5047,5147,5200,5284,5352,5441,5530,5592,5657,5720,5790,5897,5997,6097,6193,6283,6369,6429,6487,6567,6657,6732,6813"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,52,53,54,69,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3179,3253,3337,3430,4248,4347,4472,4869,4932,4999,6819,7054,7185,7264,7351,7415,7481,7541,7610,7671,7746,7823,7885,7939,8054,8113,8173,8227,8299,8655,8743,8822,8920,9008,9092,9230,9308,9384,9523,9617,9697,9753,9807,9873,9946,10024,10095,10179,10252,10330,10403,10478,10588,10678,10753,10847,10945,11019,11096,11196,11249,11333,11401,11490,11579,11641,11706,11769,11839,11946,12046,12146,12242,12332,12418,12478,12536,13022,13112,13187", "endLines": "5,34,35,36,37,38,46,47,48,52,53,54,69,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,78,86,63,65,59,68,60,74,76,61,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,89,85,59,57,79,89,74,80", "endOffsets": "318,3174,3248,3332,3425,3519,4342,4467,4555,4927,4994,5091,6883,7112,7259,7346,7410,7476,7536,7605,7666,7741,7818,7880,7934,8049,8108,8168,8222,8294,8424,8738,8817,8915,9003,9087,9225,9303,9379,9518,9612,9692,9748,9802,9868,9941,10019,10090,10174,10247,10325,10398,10473,10583,10673,10748,10842,10940,11014,11091,11191,11244,11328,11396,11485,11574,11636,11701,11764,11834,11941,12041,12141,12237,12327,12413,12473,12531,12611,13107,13182,13263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,258,379,512,651,793,926,1064,1168,1320,1470", "endColumns": "111,90,120,132,138,141,132,137,103,151,149,120", "endOffsets": "162,253,374,507,646,788,921,1059,1163,1315,1465,1586"}, "to": {"startLines": "49,51,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4560,4778,5414,5535,5668,5807,5949,6082,6220,6324,6476,6626", "endColumns": "111,90,120,132,138,141,132,137,103,151,149,120", "endOffsets": "4667,4864,5530,5663,5802,5944,6077,6215,6319,6471,6621,6742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3620,3722,3821,3920,4026,4130,13908", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3615,3717,3816,3915,4021,4125,4243,14004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "33,68,70,71,73,91,92,93,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3026,6747,6888,6964,7117,8429,8497,8574,12616,12700,12780,12852,13268,13355,13434,13515,13595,13672,13750,13824,14009,14083,14163,14234", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3096,6814,6959,7049,7180,8492,8569,8650,12695,12775,12847,12935,13350,13429,13510,13590,13667,13745,13819,13903,14078,14158,14229,14312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,12940", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,13017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "50,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4672,5096,5199,5309", "endColumns": "105,102,109,104", "endOffsets": "4773,5194,5304,5409"}}]}]}