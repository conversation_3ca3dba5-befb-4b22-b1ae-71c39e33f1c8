import React from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Text, View } from 'tamagui'
import { AppBar } from '../../components/ui/AppBar'
import { AppIcons } from '../../components/ui/Icons'

export default function BookingsScreen() {
  const insets = useSafeAreaInsets()

  return (
    <View flex={1} backgroundColor="$background" style={{ paddingTop: insets.top }}>
      <AppBar
        title="Bookings"
        endIcons={[
          {
            icon: <AppIcons.filter size={20} />,
            onPress: () => console.log('Filter pressed'),
          },
          {
            icon: <AppIcons.settings size={20} />,
            onPress: () => console.log('Settings pressed'),
          },
        ]}
      />

      <View flex={1} justifyContent="center" alignItems="center" padding="$lg">
        <AppIcons.calendar size={64} color="$colorSecondary" />
        <Text fontSize="$headlineMedium" fontWeight="600" color="$color" marginTop="$lg">
          Bookings
        </Text>
        <Text fontSize="$bodyLarge" color="$colorSecondary" textAlign="center" marginTop="$md">
          View and manage all your bookings and reservations
        </Text>
      </View>
    </View>
  )
}
