import { createFont, createTamagui } from '@tamagui/core'
import { darkTheme, lightTheme } from './theme/themes'
import { fonts, tokens } from './theme/tokens'

// Create Changa font configuration
const changaFont = createFont({
  family: 'Changa-Regular, Changa, system-ui, sans-serif',
  size: {
    1: 10,
    2: 11,
    3: 12,
    4: 14,
    5: 16,
    6: 18,
    7: 20,
    8: 22,
    9: 24,
    10: 28,
    11: 32,
    12: 36,
    13: 40,
    14: 48,
    15: 56,
    16: 64,
    // Typography scale
    labelSmall: 10,
    labelMedium: 12,
    labelLarge: 14,
    bodySmall: 12,
    bodyMedium: 14,
    bodyLarge: 16,
    titleSmall: 12,
    titleMedium: 14,
    titleLarge: 16,
    headlineSmall: 18,
    headlineMedium: 20,
    headlineLarge: 22,
    displaySmall: 24,
    displayMedium: 28,
    displayLarge: 32,
  },
  lineHeight: {
    1: 14,
    2: 16,
    3: 18,
    4: 20,
    5: 22,
    6: 24,
    7: 26,
    8: 28,
    9: 32,
    10: 36,
    11: 40,
    12: 44,
    13: 48,
    14: 56,
    15: 64,
    16: 72,
    // Typography line heights
    labelSmall: 16,
    labelMedium: 18,
    labelLarge: 20,
    bodySmall: 18,
    bodyMedium: 20,
    bodyLarge: 24,
    titleSmall: 18,
    titleMedium: 20,
    titleLarge: 22,
    headlineSmall: 24,
    headlineMedium: 26,
    headlineLarge: 28,
    displaySmall: 32,
    displayMedium: 36,
    displayLarge: 40,
  },
  weight: {
    1: '300', // Light
    2: '400', // Regular
    3: '500', // Medium
    4: '600', // SemiBold
    5: '700', // Bold
    6: '800', // ExtraBold
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  letterSpacing: {
    1: 0,
    2: -0.5,
    3: -1,
    4: -1.5,
    5: -2,
  },
  face: {
    300: { normal: 'Changa-Light' },
    400: { normal: 'Changa-Regular' },
    500: { normal: 'Changa-Medium' },
    600: { normal: 'Changa-SemiBold' },
    700: { normal: 'Changa-Bold' },
    800: { normal: 'Changa-ExtraBold' },
  },
})

// Dalti Custom Tamagui Configuration
const config = createTamagui({
  themes: {
    light: lightTheme,
    dark: darkTheme,
  },
  tokens: {
    space: tokens.space,
    size: {
      ...tokens.size,
      // Ensure input border width is available
      inputBorderWidth: 1,
      inputFocusBorderWidth: 2,
    },
    radius: tokens.radius,
    zIndex: tokens.zIndex,
    color: tokens.color,
    // Add fontSize tokens from our font configuration
    fontSize: {
      // Display Styles
      displayLarge: fonts.body.size.displayLarge,
      displayMedium: fonts.body.size.displayMedium,
      displaySmall: fonts.body.size.displaySmall,

      // Headline Styles
      headlineLarge: fonts.body.size.headlineLarge,
      headlineMedium: fonts.body.size.headlineMedium,
      headlineSmall: fonts.body.size.headlineSmall,

      // Title Styles
      titleLarge: fonts.body.size.titleLarge,
      titleMedium: fonts.body.size.titleMedium,
      titleSmall: fonts.body.size.titleSmall,

      // Body Styles
      bodyLarge: fonts.body.size.bodyLarge,
      bodyMedium: fonts.body.size.bodyMedium,
      bodySmall: fonts.body.size.bodySmall,

      // Label Styles
      labelLarge: fonts.body.size.labelLarge,
      labelMedium: fonts.body.size.labelMedium,
      labelSmall: fonts.body.size.labelSmall,

      // Legacy HTML-style tokens for compatibility
      h1: fonts.body.size.headlineLarge,
      h2: fonts.body.size.headlineMedium,
      h3: fonts.body.size.headlineSmall,
      h4: fonts.body.size.titleLarge,
      h5: fonts.body.size.titleMedium,
      h6: fonts.body.size.titleSmall,
    },
  },
  fonts: {
    body: changaFont,
    heading: changaFont,
  },
  defaultFont: 'body',
  settings: {
    allowedStyleValues: 'somewhat-strict',
    autocompleteSpecificTokens: 'except-special',
  },
  media: {
    xs: { maxWidth: 660 },
    sm: { maxWidth: 800 },
    md: { maxWidth: 1020 },
    lg: { maxWidth: 1280 },
    xl: { maxWidth: 1420 },
    xxl: { maxWidth: 1600 },
    gtXs: { minWidth: 660 + 1 },
    gtSm: { minWidth: 800 + 1 },
    gtMd: { minWidth: 1020 + 1 },
    gtLg: { minWidth: 1280 + 1 },
    short: { maxHeight: 820 },
    tall: { minHeight: 820 },
    hoverNone: { hover: 'none' },
    pointerCoarse: { pointer: 'coarse' },
  },
  shorthands: {
    // Layout
    p: 'padding',
    pt: 'paddingTop',
    pr: 'paddingRight',
    pb: 'paddingBottom',
    pl: 'paddingLeft',
    px: 'paddingHorizontal',
    py: 'paddingVertical',
    m: 'margin',
    mt: 'marginTop',
    mr: 'marginRight',
    mb: 'marginBottom',
    ml: 'marginLeft',
    mx: 'marginHorizontal',
    my: 'marginVertical',

    // Sizing
    w: 'width',
    h: 'height',
    minW: 'minWidth',
    minH: 'minHeight',
    maxW: 'maxWidth',
    maxH: 'maxHeight',

    // Colors
    bg: 'backgroundColor',
    c: 'color',

    // Border
    br: 'borderRadius',
    btlr: 'borderTopLeftRadius',
    btrr: 'borderTopRightRadius',
    bblr: 'borderBottomLeftRadius',
    bbrr: 'borderBottomRightRadius',
    bw: 'borderWidth',
    bc: 'borderColor',

    // Flexbox
    ai: 'alignItems',
    ac: 'alignContent',
    jc: 'justifyContent',
    as: 'alignSelf',
    fd: 'flexDirection',
    fw: 'flexWrap',
    fg: 'flexGrow',
    fs: 'flexShrink',
    fb: 'flexBasis',

    // Position
    pos: 'position',
    t: 'top',
    r: 'right',
    b: 'bottom',
    l: 'left',
    zi: 'zIndex',
  },
})

export default config

export type Conf = typeof config

declare module '@tamagui/core' {
  interface TamaguiCustomConfig extends Conf {}
}
