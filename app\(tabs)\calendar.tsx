import React from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Text, View } from 'tamagui'
import { AppBar } from '../../components/ui/AppBar'
import { AppIcons } from '../../components/ui/Icons'

export default function CalendarScreen() {
  const insets = useSafeAreaInsets()

  return (
    <View flex={1} backgroundColor="$background" style={{ paddingTop: insets.top }}>
      <AppBar
        title="Calendar"
        endIcon={{
          icon: <AppIcons.settings size={20} />,
          onPress: () => console.log('Settings pressed'),
        }}
      />

      <View flex={1} justifyContent="center" alignItems="center" padding="$lg">
        <AppIcons.calendar size={64} color="$colorSecondary" />
        <Text fontSize="$headlineMedium" fontWeight="600" color="$color" marginTop="$lg">
          Calendar
        </Text>
        <Text fontSize="$bodyLarge" color="$colorSecondary" textAlign="center" marginTop="$md">
          Manage your appointments and schedule
        </Text>
      </View>
    </View>
  )
}
