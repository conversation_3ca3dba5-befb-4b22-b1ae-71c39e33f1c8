import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_CONFIG, API_ENDPOINTS, ERROR_MESSAGES, HTTP_STATUS } from '../../constants/api.constants';
import { ApiError, ApiResponse } from '../../types/api.types';
import { storageService } from '../storage/storage.service';

export class ApiClient {
  private static instance: ApiClient;
  private axiosInstance: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: {
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }[] = [];

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  public static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const token = await storageService.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        if (__DEV__) {
          console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
          if (config.data) {
            console.log('📤 Request Data:', config.data);
          }
        }

        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        if (__DEV__) {
          console.log(`✅ API Response: ${response.status} ${response.config.url}`);
          console.log('📥 Response Data:', response.data);
        }
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        if (__DEV__) {
          console.error(`❌ API Error: ${error.response?.status} ${error.config?.url}`);
          console.error('📥 Error Response:', error.response?.data);
        }

        // Handle token refresh for 401 errors
        if (error.response?.status === HTTP_STATUS.UNAUTHORIZED && !originalRequest._retry) {
          if (this.isRefreshing) {
            // If already refreshing, queue the request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then(token => {
              if (originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${token}`;
              }
              return this.axiosInstance(originalRequest);
            }).catch(err => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = await storageService.getRefreshToken();
            if (refreshToken) {
              const response = await this.axiosInstance.post(API_ENDPOINTS.AUTH.REFRESH_TOKEN, {
                refreshToken,
              });

              const { sessionId } = response.data.data;
              await storageService.setAuthToken(sessionId);

              // Process failed queue
              this.processQueue(null, sessionId);

              if (originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${sessionId}`;
              }
              return this.axiosInstance(originalRequest);
            }
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            await this.handleAuthFailure();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  private async handleAuthFailure(): Promise<void> {
    await storageService.clearAuthTokens();
    // Navigate to login screen - this will be handled by the auth store
  }

  private handleError(error: AxiosError): ApiError {
    const response = error.response;
    const data = response?.data as any;

    // Network error
    if (!response) {
      return {
        success: false,
        message: ERROR_MESSAGES.NETWORK_ERROR,
        status: 0,
      };
    }

    // Timeout error
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        message: ERROR_MESSAGES.TIMEOUT_ERROR,
        status: 0,
      };
    }

    // Server provided error message
    if (data?.message) {
      return {
        success: false,
        message: data.message,
        errors: data.errors,
        status: response.status,
      };
    }

    // Default error messages based on status code
    switch (response.status) {
      case HTTP_STATUS.UNAUTHORIZED:
        return {
          success: false,
          message: ERROR_MESSAGES.UNAUTHORIZED,
          status: response.status,
        };
      case HTTP_STATUS.FORBIDDEN:
        return {
          success: false,
          message: ERROR_MESSAGES.FORBIDDEN,
          status: response.status,
        };
      case HTTP_STATUS.NOT_FOUND:
        return {
          success: false,
          message: ERROR_MESSAGES.NOT_FOUND,
          status: response.status,
        };
      case HTTP_STATUS.UNPROCESSABLE_ENTITY:
        return {
          success: false,
          message: ERROR_MESSAGES.VALIDATION_ERROR,
          errors: data?.errors,
          status: response.status,
        };
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
      case HTTP_STATUS.SERVICE_UNAVAILABLE:
        return {
          success: false,
          message: ERROR_MESSAGES.SERVER_ERROR,
          status: response.status,
        };
      default:
        return {
          success: false,
          message: ERROR_MESSAGES.UNKNOWN_ERROR,
          status: response.status,
        };
    }
  }

  // HTTP Methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.put(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.patch(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.delete(url, config);
    return response.data;
  }

  // Utility methods
  setAuthToken(token: string): void {
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  removeAuthToken(): void {
    delete this.axiosInstance.defaults.headers.common['Authorization'];
  }

  // File upload
  async uploadFile<T>(
    url: string,
    file: FormData,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post(url, file, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
}

// Export singleton instance
export const apiClient = ApiClient.getInstance();
