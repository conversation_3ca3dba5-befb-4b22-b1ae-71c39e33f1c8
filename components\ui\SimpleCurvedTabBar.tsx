import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { CurvedBottomBar } from 'react-native-curved-bottom-bar';
import { Text } from 'tamagui';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const SimpleCurvedTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation
}) => {
  const { isDark } = useTheme();

  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const centerButtonBg = isDark ? '#257587' : '#15424E';

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  const _renderIcon = (routeName: string, selectedTab: string) => {
    const isActive = routeName === selectedTab;
    return (
      <View style={styles.tabItem}>
        <TabBarIcon
          name={routeName}
          focused={isActive}
          color={isActive ? activeColor : inactiveColor}
          size={24}
        />
        <Text
          fontSize={12}
          color={isActive ? activeColor : inactiveColor}
          marginTop={4}
        >
          {getTabLabel(routeName)}
        </Text>
      </View>
    );
  };

  const renderTabBar = ({ routeName, selectedTab, navigate }: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigate(routeName)}
        style={styles.tabbarItem}
      >
        {_renderIcon(routeName, selectedTab)}
      </TouchableOpacity>
    );
  };

  const renderCircle = ({ selectedTab, navigate }: any) => {
    return (
      <View style={[styles.btnCircleUp, { backgroundColor: centerButtonBg }]}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => Alert.alert('QR Scanner', 'QR Scanner functionality')}
        >
          <AppIcons.qrCode size={28} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    );
  };

  // Get current route name
  const currentRouteName = state.routes[state.index]?.name || 'index';

  return (
    <View style={styles.container}>
      <CurvedBottomBar.Navigator
        type="DOWN"
        style={[styles.bottomBar, { backgroundColor: tabBarBg }]}
        shadowStyle={styles.shadow}
        height={70}
        circleWidth={60}
        bgColor={tabBarBg}
        initialRouteName={currentRouteName}
        borderTopLeftRight
        renderCircle={renderCircle}
        tabBar={renderTabBar}
      >
        <CurvedBottomBar.Screen
          name="index"
          position="LEFT"
          component={() => <View />}
        />
        <CurvedBottomBar.Screen
          name="calendar"
          position="LEFT"
          component={() => <View />}
        />
        <CurvedBottomBar.Screen
          name="clients"
          position="RIGHT"
          component={() => <View />}
        />
        <CurvedBottomBar.Screen
          name="bookings"
          position="RIGHT"
          component={() => <View />}
        />
      </CurvedBottomBar.Navigator>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 90,
  },
  bottomBar: {
    flex: 1,
  },
  shadow: {
    shadowColor: '#DDDDDD',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 5,
  },
  button: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnCircleUp: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    bottom: 18,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 1,
  },
  tabbarItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
