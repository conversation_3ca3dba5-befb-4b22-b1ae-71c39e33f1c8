import { Text, View } from '@tamagui/core'
import React, { forwardRef, useState } from 'react'
import { TextInput } from 'react-native'
import { useTheme } from '../../theme/DaltiThemeProvider'
import { AppIcons } from './Icons'
import { Input, InputProps } from './Input'

export interface PasswordInputProps extends Omit<InputProps, 'secureTextEntry' | 'rightIcon' | 'onRightIconPress'> {
  showPasswordToggle?: boolean
  iconSize?: number
  showStrengthIndicator?: boolean
}

export const PasswordInput = forwardRef<TextInput, PasswordInputProps>(({
  showPasswordToggle = true,
  iconSize = 20,
  showStrengthIndicator = false,
  placeholder = 'Enter password',
  value,
  onChangeText,
  ...inputProps
}, ref) => {
  const { isDark } = useTheme()
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [password, setPassword] = useState(value || '')

  const handleChangeText = (text: string) => {
    setPassword(text)
    onChangeText?.(text)
  }

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible)
  }

  // Simple password strength calculation
  const getPasswordStrength = (pwd: string) => {
    let strength = 0
    if (pwd.length >= 8) strength++
    if (/[A-Z]/.test(pwd)) strength++
    if (/[a-z]/.test(pwd)) strength++
    if (/[0-9]/.test(pwd)) strength++
    if (/[^A-Za-z0-9]/.test(pwd)) strength++
    return strength
  }

  const getStrengthColor = (strength: number) => {
    switch (strength) {
      case 0:
      case 1:
        return '$error'
      case 2:
      case 3:
        return '$warning'
      case 4:
      case 5:
        return '$success'
      default:
        return '$colorSecondary'
    }
  }

  const getStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1:
        return 'Weak'
      case 2:
      case 3:
        return 'Medium'
      case 4:
      case 5:
        return 'Strong'
      default:
        return ''
    }
  }

  const iconColor = isDark ? '#B8BCC8' : '#A0A0A0'
  const passwordStrength = getPasswordStrength(password)

  return (
    <View gap="$xs">
      <Input
        ref={ref}
        placeholder={placeholder}
        value={password}
        onChangeText={handleChangeText}
        secureTextEntry={!isPasswordVisible}
        rightIcon={
          showPasswordToggle ? (
            isPasswordVisible ? (
              <AppIcons.eyeOff size={iconSize} color={iconColor} />
            ) : (
              <AppIcons.eye size={iconSize} color={iconColor} />
            )
          ) : undefined
        }
        onRightIconPress={showPasswordToggle ? togglePasswordVisibility : undefined}
        {...inputProps}
      />

      {/* Password Strength Indicator */}
      {showStrengthIndicator && password.length > 0 && (
        <View gap="$xs">
          {/* Strength Bars */}
          <View flexDirection="row" gap="$xs">
            {[1, 2, 3, 4, 5].map((level) => (
              <View
                key={level}
                flex={1}
                height={3}
                backgroundColor={
                  level <= passwordStrength 
                    ? getStrengthColor(passwordStrength)
                    : '$borderColor'
                }
                borderRadius="$full"
              />
            ))}
          </View>

          {/* Strength Text */}
          <Text 
            fontSize="$labelSmall" 
            color={getStrengthColor(passwordStrength)}
          >
            Password strength: {getStrengthText(passwordStrength)}
          </Text>

          {/* Password Requirements */}
          {passwordStrength < 4 && (
            <View gap="$xs">
              <Text fontSize="$labelSmall" color="$colorSecondary">
                Password should contain:
              </Text>
              <View gap="$xs" paddingLeft="$sm">
                <Text 
                  fontSize="$labelSmall" 
                  color={password.length >= 8 ? '$success' : '$colorSecondary'}
                >
                  • At least 8 characters
                </Text>
                <Text 
                  fontSize="$labelSmall" 
                  color={/[A-Z]/.test(password) ? '$success' : '$colorSecondary'}
                >
                  • One uppercase letter
                </Text>
                <Text 
                  fontSize="$labelSmall" 
                  color={/[a-z]/.test(password) ? '$success' : '$colorSecondary'}
                >
                  • One lowercase letter
                </Text>
                <Text 
                  fontSize="$labelSmall" 
                  color={/[0-9]/.test(password) ? '$success' : '$colorSecondary'}
                >
                  • One number
                </Text>
                <Text 
                  fontSize="$labelSmall" 
                  color={/[^A-Za-z0-9]/.test(password) ? '$success' : '$colorSecondary'}
                >
                  • One special character
                </Text>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  )
})

PasswordInput.displayName = 'PasswordInput'
