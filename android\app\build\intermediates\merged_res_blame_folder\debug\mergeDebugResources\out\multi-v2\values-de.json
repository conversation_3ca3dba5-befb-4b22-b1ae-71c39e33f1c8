{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,289,357,439,506,582,658,741,828,909,992,1072,1158,1243,1321,1392,1462,1553,1628,1703", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "125,208,284,352,434,501,577,653,736,823,904,987,1067,1153,1238,1316,1387,1457,1548,1623,1698,1776"}, "to": {"startLines": "33,49,69,71,72,91,92,93,142,143,144,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3060,4606,6898,7044,7112,8537,8604,8680,12712,12795,12882,13281,13364,13444,13530,13615,13693,13764,13834,14026,14101,14176", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "3130,4684,6969,7107,7189,8599,8675,8751,12790,12877,12958,13359,13439,13525,13610,13688,13759,13829,13920,14096,14171,14249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,259,378,510,645,786,908,1065,1164,1310,1498", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "159,254,373,505,640,781,903,1060,1159,1305,1493,1621"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4689,4902,5531,5650,5782,5917,6058,6180,6337,6436,6582,6770", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "4793,4992,5645,5777,5912,6053,6175,6332,6431,6577,6765,6893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3571,3669,3771,3871,3971,4079,4184,13925", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3664,3766,3866,3966,4074,4179,4297,14021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,12963", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,13040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4798,5219,5320,5431", "endColumns": "103,100,110,99", "endOffsets": "4897,5315,5426,5526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1456,1564,1630,1699,1757,1829,1893,1960,2030,2085,2139,2267,2327,2389,2443,2521,2658,2750,2828,2922,3008,3092,3237,3321,3407,3540,3630,3709,3766,3817,3883,3957,4039,4110,4185,4259,4337,4409,4483,4593,4685,4767,4856,4945,5019,5097,5183,5238,5317,5384,5464,5548,5610,5674,5737,5806,5913,6020,6119,6225,6321,6416,6477,6532,6614,6697,6774", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,81,107,65,68,57,71,63,66,69,54,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,95,94,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1451,1559,1625,1694,1752,1824,1888,1955,2025,2080,2134,2262,2322,2384,2438,2516,2653,2745,2823,2917,3003,3087,3232,3316,3402,3535,3625,3704,3761,3812,3878,3952,4034,4105,4180,4254,4332,4404,4478,4588,4680,4762,4851,4940,5014,5092,5178,5233,5312,5379,5459,5543,5605,5669,5732,5801,5908,6015,6114,6220,6316,6411,6472,6527,6609,6692,6769,6845"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3135,3226,3315,3399,3489,4302,4403,4525,4997,5059,5125,6974,7194,7253,7335,7443,7509,7578,7636,7708,7772,7839,7909,7964,8018,8146,8206,8268,8322,8400,8756,8848,8926,9020,9106,9190,9335,9419,9505,9638,9728,9807,9864,9915,9981,10055,10137,10208,10283,10357,10435,10507,10581,10691,10783,10865,10954,11043,11117,11195,11281,11336,11415,11482,11562,11646,11708,11772,11835,11904,12011,12118,12217,12323,12419,12514,12575,12630,13045,13128,13205", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,146,147,148", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,81,107,65,68,57,71,63,66,69,54,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,95,94,60,54,81,82,76,75", "endOffsets": "328,3221,3310,3394,3484,3566,4398,4520,4601,5054,5120,5214,7039,7248,7330,7438,7504,7573,7631,7703,7767,7834,7904,7959,8013,8141,8201,8263,8317,8395,8532,8843,8921,9015,9101,9185,9330,9414,9500,9633,9723,9802,9859,9910,9976,10050,10132,10203,10278,10352,10430,10502,10576,10686,10778,10860,10949,11038,11112,11190,11276,11331,11410,11477,11557,11641,11703,11767,11830,11899,12006,12113,12212,12318,12414,12509,12570,12625,12707,13123,13200,13276"}}]}]}