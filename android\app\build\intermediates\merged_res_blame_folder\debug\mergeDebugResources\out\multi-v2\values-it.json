{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1489,1577,1639,1708,1771,1844,1907,1977,2051,2108,2162,2283,2340,2402,2456,2533,2670,2755,2835,2934,3020,3102,3237,3318,3399,3546,3637,3727,3782,3833,3899,3972,4052,4123,4203,4278,4355,4424,4501,4606,4694,4783,4876,4969,5043,5123,5217,5268,5352,5418,5502,5590,5652,5716,5779,5847,5962,6076,6182,6291,6409,6522,6581,6636,6716,6801,6880", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,90,87,61,68,62,72,62,69,73,56,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,146,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,117,112,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1484,1572,1634,1703,1766,1839,1902,1972,2046,2103,2157,2278,2335,2397,2451,2528,2665,2750,2830,2929,3015,3097,3232,3313,3394,3541,3632,3722,3777,3828,3894,3967,4047,4118,4198,4273,4350,4419,4496,4601,4689,4778,4871,4964,5038,5118,5212,5263,5347,5413,5497,5585,5647,5711,5774,5842,5957,6071,6177,6286,6404,6517,6576,6631,6711,6796,6875,6957"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3082,3166,3247,3324,3423,4265,4364,4504,4977,5041,5107,6937,7182,7244,7335,7423,7485,7554,7617,7690,7753,7823,7897,7954,8008,8129,8186,8248,8302,8379,8680,8765,8845,8944,9030,9112,9247,9328,9409,9556,9647,9737,9792,9843,9909,9982,10062,10133,10213,10288,10365,10434,10511,10616,10704,10793,10886,10979,11053,11133,11227,11278,11362,11428,11512,11600,11662,11726,11789,11857,11972,12086,12192,12301,12419,12532,12591,12646,13067,13152,13231", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,145,146,147", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,90,87,61,68,62,72,62,69,73,56,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,146,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,117,112,58,54,79,84,78,81", "endOffsets": "313,3161,3242,3319,3418,3513,4359,4499,4582,5036,5102,5197,7017,7239,7330,7418,7480,7549,7612,7685,7748,7818,7892,7949,8003,8124,8181,8243,8297,8374,8511,8760,8840,8939,9025,9107,9242,9323,9404,9551,9642,9732,9787,9838,9904,9977,10057,10128,10208,10283,10360,10429,10506,10611,10699,10788,10881,10974,11048,11128,11222,11273,11357,11423,11507,11595,11657,11721,11784,11852,11967,12081,12187,12296,12414,12527,12586,12641,12721,13147,13226,13308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,374,509,655,811,941,1099,1201,1338,1489", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "161,256,369,504,650,806,936,1094,1196,1333,1484,1609"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4671,4882,5513,5626,5761,5907,6063,6193,6351,6453,6590,6741", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "4777,4972,5621,5756,5902,6058,6188,6346,6448,6585,6736,6861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4782,5202,5300,5410", "endColumns": "99,97,109,102", "endOffsets": "4877,5295,5405,5508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,12985", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,13062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,49,69,71,72,91,92,141,142,143,148,149,150,151,152,153,154,155,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,4587,6866,7022,7094,8516,8596,12726,12816,12897,13313,13399,13476,13556,13635,13710,13780,13849,14040,14115,14196", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,4666,6932,7089,7177,8591,8675,12811,12892,12980,13394,13471,13551,13630,13705,13775,13844,13934,14110,14191,14278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "39,40,41,42,43,44,45,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3518,3616,3718,3817,3919,4028,4135,13939", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3611,3713,3812,3914,4023,4130,4260,14035"}}]}]}