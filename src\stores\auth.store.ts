import { create } from 'zustand';
import { devtools, persist, StateStorage } from 'zustand/middleware';
import { AuthService } from '../features/auth/services/auth.service';
import { apiClient } from '../services/api/api.client';
import { storageService } from '../services/storage/storage.service';
import { AuthUser, Provider } from '../types/api.types';

// Custom storage adapter that handles errors gracefully
const createCustomStorage = (): StateStorage => ({
  getItem: (name: string): string | null => {
    try {
      return storageService.getItem(name);
    } catch (error) {
      console.warn(`[zustand persist middleware] Unable to get item '${name}':`, error);
      return null;
    }
  },
  setItem: (name: string, value: string): void => {
    try {
      storageService.setItem(name, value);
    } catch (error) {
      console.warn(`[zustand persist middleware] Unable to update item '${name}':`, error);
    }
  },
  removeItem: (name: string): void => {
    try {
      storageService.removeItem(name);
    } catch (error) {
      console.warn(`[zustand persist middleware] Unable to remove item '${name}':`, error);
    }
  },
});

export interface AuthState {
  // State
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AuthUser | null;
  provider: Provider | null;
  error: string | null;
  isInitialized: boolean;

  // Actions
  initialize: () => Promise<void>;
  login: (identifier: string, password: string) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  verifyOTP: (data: OTPData) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  clearError: () => void;
  updateUser: (user: Partial<AuthUser>) => void;
  updateProvider: (provider: Partial<Provider>) => void;
}

export interface RegisterData {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface OTPData {
  otp: string;
  identifier: string;
  password: string;
  firstName: string;
  lastName: string;
  providerCategoryId: number;
  businessName: string;
  phone: string;
  email: string;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        isAuthenticated: false,
        isLoading: false,
        user: null,
        provider: null,
        error: null,
        isInitialized: false,

        // Initialize auth state from storage
        initialize: async () => {
          try {
            set({ isLoading: true });

            console.log('🔄 Auth Store: Initializing auth state...');

            const token = await storageService.getAuthToken();
            const userData = await storageService.getUserData<AuthUser>();
            const providerData = await storageService.getProviderData<Provider>();

            console.log('🔍 Auth Store: Retrieved data:', {
              hasToken: !!token,
              hasUserData: !!userData,
              hasProviderData: !!providerData,
              tokenLength: token?.length || 0,
            });

            if (token && userData && providerData) {
              // Verify token is still valid
              try {
                apiClient.setAuthToken(token);
                console.log('✅ Auth Store: Token restored, user authenticated');
                // You could make a quick API call here to verify the token
                set({
                  isAuthenticated: true,
                  user: userData,
                  provider: providerData,
                  isInitialized: true,
                  isLoading: false,
                });
              } catch (error) {
                console.log('❌ Auth Store: Token invalid, logging out');
                // Token is invalid, clear everything
                await get().logout();
              }
            } else {
              console.log('❌ Auth Store: Missing auth data, user not authenticated');
              set({
                isAuthenticated: false,
                user: null,
                provider: null,
                isInitialized: true,
                isLoading: false,
              });
            }
          } catch (error) {
            console.error('Auth initialization error:', error);
            set({
              isAuthenticated: false,
              user: null,
              provider: null,
              isInitialized: true,
              isLoading: false,
              error: 'Failed to initialize authentication',
            });
          }
        },

        // Login action
        login: async (identifier: string, password: string): Promise<boolean> => {
          try {
            set({ isLoading: true, error: null });

            const authService = AuthService.getInstance();
            const response = await authService.login({
              identifier,
              password,
            });

            if (response.success && response.data) {
              const { sessionId, user, provider } = response.data;

              console.log('💾 Auth Store: Saving login data...', {
                sessionIdLength: sessionId?.length || 0,
                hasUser: !!user,
                hasProvider: !!provider,
              });

              // Store tokens and user data
              await storageService.setAuthToken(sessionId);
              storageService.setUserData(user);
              storageService.setProviderData(provider);

              console.log('✅ Auth Store: Login data saved successfully');

              // Update API client
              apiClient.setAuthToken(sessionId);

              set({
                isAuthenticated: true,
                user,
                provider,
                isLoading: false,
                error: null,
              });

              return true;
            } else {
              set({
                isLoading: false,
                error: response.message || 'Login failed',
              });
              return false;
            }
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Login failed',
            });
            return false;
          }
        },

        // Register action - Step 1: Request OTP
        register: async (data: RegisterData): Promise<boolean> => {
          try {
            set({ isLoading: true, error: null });

            const authService = AuthService.getInstance();
            const response = await authService.requestEmailOTP({
              ...data,
              isProviderRegistration: true,
            });

            if (response.success) {
              set({
                isLoading: false,
                error: null,
              });
              return true;
            } else {
              set({
                isLoading: false,
                error: response.message || 'Failed to send OTP',
              });
              return false;
            }
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Failed to send OTP',
            });
            return false;
          }
        },

        // OTP Verification action - Step 2: Complete registration
        verifyOTP: async (data: OTPData): Promise<boolean> => {
          try {
            set({ isLoading: true, error: null });

            const authService = AuthService.getInstance();
            const response = await authService.verifyOTPAndRegister(data);

            if (response.success && response.data) {
              const { sessionId, user, provider } = response.data;

              // Store tokens and user data
              await storageService.setAuthToken(sessionId);
              storageService.setUserData(user);
              storageService.setProviderData(provider);

              // Update API client
              apiClient.setAuthToken(sessionId);

              set({
                isAuthenticated: true,
                user,
                provider,
                isLoading: false,
                error: null,
              });

              return true;
            } else {
              set({
                isLoading: false,
                error: response.message || 'OTP verification failed',
              });
              return false;
            }
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'OTP verification failed',
            });
            return false;
          }
        },

        // Logout action
        logout: async () => {
          try {
            set({ isLoading: true });

            // Call logout API if authenticated
            if (get().isAuthenticated) {
              try {
                await apiClient.post('/auth/logout');
              } catch (error) {
                // Continue with logout even if API call fails
                console.error('Logout API error:', error);
              }
            }

            // Clear all stored data
            await storageService.clearAllData();
            apiClient.removeAuthToken();

            set({
              isAuthenticated: false,
              user: null,
              provider: null,
              isLoading: false,
              error: null,
            });
          } catch (error) {
            console.error('Logout error:', error);
            // Force logout even if there's an error
            await storageService.clearAllData();
            apiClient.removeAuthToken();

            set({
              isAuthenticated: false,
              user: null,
              provider: null,
              isLoading: false,
              error: null,
            });
          }
        },

        // Refresh token action
        refreshToken: async (): Promise<boolean> => {
          try {
            const refreshToken = await storageService.getRefreshToken();
            if (!refreshToken) {
              await get().logout();
              return false;
            }

            const response = await apiClient.post<{ sessionId: string }>('/auth/refresh', {
              refreshToken,
            });

            if (response.success && response.data) {
              const { sessionId } = response.data;
              await storageService.setAuthToken(sessionId);
              apiClient.setAuthToken(sessionId);
              return true;
            } else {
              await get().logout();
              return false;
            }
          } catch (error) {
            console.error('Token refresh error:', error);
            await get().logout();
            return false;
          }
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Update user data
        updateUser: (userData: Partial<AuthUser>) => {
          const currentUser = get().user;
          if (currentUser) {
            const updatedUser = { ...currentUser, ...userData };
            storageService.setUserData(updatedUser);
            set({ user: updatedUser });
          }
        },

        // Update provider data
        updateProvider: (providerData: Partial<Provider>) => {
          const currentProvider = get().provider;
          if (currentProvider) {
            const updatedProvider = { ...currentProvider, ...providerData };
            storageService.setProviderData(updatedProvider);
            set({ provider: updatedProvider });
          }
        },
      }),
      {
        name: 'auth-store',
        storage: createCustomStorage(),
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          provider: state.provider,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);
