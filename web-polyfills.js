// Comprehensive polyfill for import.meta in web environments

// Define import.meta globally
if (typeof globalThis !== 'undefined') {
  if (!globalThis.import) {
    globalThis.import = {};
  }
  if (!globalThis.import.meta) {
    globalThis.import.meta = {
      url: typeof window !== 'undefined' ? window.location.href : 'file:///',
      env: typeof process !== 'undefined' ? process.env : {}
    };
  }
}

// Define for window object
if (typeof window !== 'undefined') {
  if (!window.import) {
    window.import = {};
  }
  if (!window.import.meta) {
    window.import.meta = {
      url: window.location.href,
      env: typeof process !== 'undefined' ? process.env : {}
    };
  }
}

// Define for global object (Node.js)
if (typeof global !== 'undefined') {
  if (!global.import) {
    global.import = {};
  }
  if (!global.import.meta) {
    global.import.meta = {
      url: 'file:///',
      env: typeof process !== 'undefined' ? process.env : {}
    };
  }
}

// Export for module systems
module.exports = {
  url: typeof window !== 'undefined' ? window.location.href : 'file:///',
  env: typeof process !== 'undefined' ? process.env : {}
};
