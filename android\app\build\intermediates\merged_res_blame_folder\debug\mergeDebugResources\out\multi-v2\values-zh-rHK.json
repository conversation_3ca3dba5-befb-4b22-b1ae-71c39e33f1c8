{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,531,600,671,744,819,886,956,1029,1101,1178,1254,1326,1396,1465,1545,1613,1683,1750", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,526,595,666,739,814,881,951,1024,1096,1173,1249,1321,1391,1460,1540,1608,1678,1745,1814"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2858,4192,6006,6132,6198,6331,7477,7542,7611,11114,11187,11262,11329,11696,11769,11841,11918,11994,12066,12136,12205,12386,12454,12524,12591", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2919,4259,6068,6193,6268,6391,7537,7606,7677,11182,11257,11324,11394,11764,11836,11913,11989,12061,12131,12200,12280,12449,12519,12586,12655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,738,840,930,1038,1141", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "148,229,328,431,533,631,733,835,925,1033,1136,1232"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4264,4445,5003,5102,5205,5307,5405,5507,5609,5699,5807,5910", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "4357,4521,5097,5200,5302,5400,5502,5604,5694,5802,5905,6001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3261,3353,3452,3546,3640,3733,3826,12285", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3348,3447,3541,3635,3728,3821,3917,12381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,11399", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,11473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4362,4717,4809,4910", "endColumns": "82,91,100,92", "endOffsets": "4440,4804,4905,4998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1236,1314,1375,1432,1488,1547,1605,1664,1725,1777,1831,1917,1973,2031,2085,2150,2243,2317,2389,2469,2543,2621,2741,2804,2867,2966,3043,3117,3167,3218,3284,3348,3416,3487,3559,3620,3691,3758,3818,3906,3986,4049,4132,4217,4291,4356,4432,4480,4554,4618,4694,4772,4834,4898,4961,5027,5107,5187,5263,5344,5420,5497,5551,5606,5675,5750,5823", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,73,77,60,56,55,58,57,58,60,51,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,75,76,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1231,1309,1370,1427,1483,1542,1600,1659,1720,1772,1826,1912,1968,2026,2080,2145,2238,2312,2384,2464,2538,2616,2736,2799,2862,2961,3038,3112,3162,3213,3279,3343,3411,3482,3554,3615,3686,3753,3813,3901,3981,4044,4127,4212,4286,4351,4427,4475,4549,4613,4689,4767,4829,4893,4956,5022,5102,5182,5258,5339,5415,5492,5546,5601,5670,5745,5818,5888"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2924,2987,3048,3115,3184,3922,4012,4119,4526,4577,4639,6073,6273,6396,6470,6548,6609,6666,6722,6781,6839,6898,6959,7011,7065,7151,7207,7265,7319,7384,7682,7756,7828,7908,7982,8060,8180,8243,8306,8405,8482,8556,8606,8657,8723,8787,8855,8926,8998,9059,9130,9197,9257,9345,9425,9488,9571,9656,9730,9795,9871,9919,9993,10057,10133,10211,10273,10337,10400,10466,10546,10626,10702,10783,10859,10936,10990,11045,11478,11553,11626", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,73,77,60,56,55,58,57,58,60,51,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,75,76,53,54,68,74,72,69", "endOffsets": "292,2982,3043,3110,3179,3256,4007,4114,4187,4572,4634,4712,6127,6326,6465,6543,6604,6661,6717,6776,6834,6893,6954,7006,7060,7146,7202,7260,7314,7379,7472,7751,7823,7903,7977,8055,8175,8238,8301,8400,8477,8551,8601,8652,8718,8782,8850,8921,8993,9054,9125,9192,9252,9340,9420,9483,9566,9651,9725,9790,9866,9914,9988,10052,10128,10206,10268,10332,10395,10461,10541,10621,10697,10778,10854,10931,10985,11040,11109,11548,11621,11691"}}]}]}