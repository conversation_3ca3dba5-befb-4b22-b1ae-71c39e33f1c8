import { Text, View } from '@tamagui/core'
import { useRouter } from 'expo-router'
import { useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { Input } from '../../../../components/ui/Input'
import { PasswordInput } from '../../../../components/ui/PasswordInput'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { useAuthStore } from '../../../stores/auth.store'

export default function LoginScreen() {
  const router = useRouter()
  const { isDark } = useTheme()

  const insets = useSafeAreaInsets()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  // Auth store
  const { login, isLoading, error, clearError } = useAuthStore()

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputContainerBg: 'rgba(58, 64, 72, 0.3)',
    inputIcons: '#257587',
    inputText: '#F8F9FA',
    inputLabels: '#B8BCC8',
    buttonBackground: '#257587',
    buttonText: '#FFFFFF',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputContainerBg: 'rgba(58, 64, 72, 0.3)',
    inputIcons: '#15424E',
    inputText: '#2C3E50',
    inputLabels: '#7F8C8D',
    buttonBackground: '#15424E',
    buttonText: '#FFFFFF',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  const handleBack = () => {
    router.back()
  }

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields')
      return
    }

    clearError()

    try {
      const success = await login(email.trim(), password)

      if (success) {
        // Navigation will be handled by AuthGuard
        console.log('Login successful')
      } else {
        Alert.alert('Login Failed', error || 'Invalid credentials')
      }
    } catch {
      Alert.alert('Error', 'An unexpected error occurred')
    }
  }

  const handleForgotPassword = () => {
    router.push('/forgot-password')
  }

  const handleSignUp = () => {
    router.push('/register')
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Welcome to Dalti Provider
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
            >
              Sign in to manage your business
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* Email Input */}
              <Input
                label="Email or Phone"
                placeholder="Email or Phone"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                variant="outlined"
                leftIcon={<AppIcons.person size={20} color={authColors.inputIcons} />}
              />

              {/* Password Input */}
              <PasswordInput
                label="Password"
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
              />

              {/* Sign In Button */}
              <Button
                title="Sign In"
                variant="primary"
                size="lg"
                fullWidth
                loading={isLoading}
                onPress={handleLogin}
                containerStyle={{ marginTop: 16 }}
              />

              {/* Forgot Password */}
              <View alignItems="center" marginTop="$lg">
                <Pressable onPress={handleForgotPassword}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Forgot Password
                  </Text>
                </Pressable>
              </View>

              {/* Sign Up Link */}
              <View 
                flexDirection="row" 
                alignItems="center" 
                justifyContent="center" 
                gap="$sm"
                marginTop="$xl"
              >
                <Text
                  fontSize="$bodyMedium"
                  color={authColors.secondaryText}
                >
                  Don&apos;t have an account?
                </Text>
                <Pressable onPress={handleSignUp}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Sign Up
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
