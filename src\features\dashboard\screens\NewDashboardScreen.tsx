import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import { Platform, Pressable, RefreshControl, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Text, View, useTheme as useTamaguiTheme } from 'tamagui';
import { AppBar } from '../../../../components/ui/AppBar';
import { AppIcons } from '../../../../components/ui/Icons';
import { LoadingSpinner } from '../../../../components/ui/LoadingSpinner';

interface DashboardCardProps {
  title: string;
  subtitle: string;
  children: React.ReactNode;
  onViewAll?: () => void;
  showRefresh?: boolean;
  onRefresh?: () => void;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  subtitle,
  children,
  onViewAll,
  showRefresh = false,
  onRefresh
}) => {
  const tamaguiTheme = useTamaguiTheme();

  // Use theme values directly - they are working correctly
  const isDarkMode = tamaguiTheme.color?.val === '#F8F9FA'; // Dark theme has light text
  const cardBg = isDarkMode ? '#1C2127' : '#FFFFFF';
  const borderColor = isDarkMode ? '#3A4048' : '#E1E8ED';

  return (
    <View
      style={{ backgroundColor: cardBg, borderColor }}
      borderRadius="$lg"
      borderWidth={1}
      padding="$lg"
      marginBottom="$lg"
    >
      {/* Header */}
      <View flexDirection="row" justifyContent="space-between" alignItems="center" marginBottom="$md">
        <View flex={1}>
          <Text fontSize="$bodyLarge" fontWeight="600" color="$color">
            {title}
          </Text>
          <View flexDirection="row" alignItems="center" marginTop="$xs">
            <View
              width={8}
              height={8}
              borderRadius={4}
              backgroundColor="$success"
              marginRight="$xs"
            />
            <Text fontSize="$bodySmall" color="$colorSecondary">
              {subtitle}
            </Text>
          </View>
        </View>
        
        <View flexDirection="row" alignItems="center" gap="$sm">
          {showRefresh && onRefresh && (
            <Pressable
              onPress={onRefresh}
              style={{
                padding: 8,
                borderRadius: 4,
                backgroundColor: 'transparent',
                ...(Platform.OS === 'web' && { cursor: 'pointer' })
              }}
            >
              <AppIcons.refresh size={20} color={tamaguiTheme.colorSecondary.val} />
            </Pressable>
          )}

          {onViewAll && (
            <Pressable
              onPress={onViewAll}
              style={{
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 4,
                borderWidth: 1,
                borderColor: tamaguiTheme.primary.val,
                ...(Platform.OS === 'web' && { cursor: 'pointer' })
              }}
            >
              <Text fontSize="$bodySmall" color="$primary">
                View All
              </Text>
            </Pressable>
          )}
        </View>
      </View>
      
      {/* Content */}
      {children}
    </View>
  );
};

interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ icon, title, subtitle }) => {
  return (
    <View alignItems="center" paddingVertical="$xl">
      <View
        width={60}
        height={60}
        borderRadius={30}
        backgroundColor="$success"
        opacity={0.1}
        alignItems="center"
        justifyContent="center"
        marginBottom="$lg"
      >
        {icon}
      </View>

      <Text fontSize="$bodyLarge" fontWeight="600" color="$color" marginBottom="$xs">
        {title}
      </Text>

      <Text fontSize="$bodyMedium" color="$colorSecondary" textAlign="center">
        {subtitle}
      </Text>
    </View>
  );
};

export const NewDashboardScreen: React.FC = () => {
  const router = useRouter();

  const tamaguiTheme = useTamaguiTheme();
  const insets = useSafeAreaInsets();

  // Debug: Log theme values (mobile-safe)
  console.log('🎨 Theme Debug:', {
    background: tamaguiTheme.background?.val,
    surface: tamaguiTheme.surface?.val,
    color: tamaguiTheme.color?.val,
    borderColor: tamaguiTheme.borderColor?.val,
    themeName: tamaguiTheme.name || 'unknown'
  });

  // Temporarily disable non-working API calls to eliminate 404 errors
  // These endpoints are not yet implemented on the backend:
  // - /provider/dashboard/active-sessions
  // - /provider/dashboard/pending-appointments
  // - /provider/appointments/today
  // TODO: Re-enable when backend endpoints are implemented
  const activeSessions = [];
  const sessionsLoading = false;
  const sessionsError = null;
  const refetchSessions = useCallback(() => {}, []);

  const pendingAppointments = [];
  const appointmentsLoading = false;
  const appointmentsError = null;
  const refetchAppointments = useCallback(() => {}, []);

  const todayAppointments = [];
  const todayLoading = false;
  const todayError = null;
  const refetchToday = useCallback(() => {}, []);

  // Uncomment when backend is ready:
  // const {
  //   data: activeSessions,
  //   isLoading: sessionsLoading,
  //   error: sessionsError,
  //   refetch: refetchSessions,
  // } = useActiveSessions();

  // const {
  //   data: pendingAppointments,
  //   isLoading: appointmentsLoading,
  //   error: appointmentsError,
  //   refetch: refetchAppointments,
  // } = usePendingAppointments();

  // const {
  //   data: todayAppointments,
  //   isLoading: todayLoading,
  //   error: todayError,
  //   refetch: refetchToday,
  // } = useTodayAppointments();

  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await Promise.all([
      refetchSessions(),
      refetchAppointments(),
      refetchToday(),
    ]);
    setRefreshing(false);
  }, [refetchSessions, refetchAppointments, refetchToday]);

  const handleViewAllSessions = () => {
    // Navigate to sessions screen
    console.log('Navigate to sessions');
  };

  const handleViewAllAppointments = () => {
    // Navigate to appointments screen
    console.log('Navigate to appointments');
  };

  const handleViewAllSchedule = () => {
    // Navigate to schedule screen
    console.log('Navigate to schedule');
  };

  // Use theme values directly - they are working correctly
  const isDarkMode = tamaguiTheme.color?.val === '#F8F9FA'; // Dark theme has light text
  const correctBg = isDarkMode ? '#0F1419' : '#FFFFFF';

  return (
    <View flex={1} style={{ backgroundColor: correctBg, paddingTop: insets.top }}>
      {/* Header using AppBar component */}
      <AppBar
        title="Dashboard"
        startIcon={{
          icon: <AppIcons.person size={20} />,
          onPress: () => {
            console.log('Profile pressed');
            router.push('/profile');
          },
        }}
        endIcons={[
          {
            icon: (
              <View position="relative">
                <AppIcons.notifications size={20} />
                <View
                  position="absolute"
                  top={-4}
                  right={-4}
                  width={12}
                  height={12}
                  borderRadius={6}
                  backgroundColor="$error"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text fontSize={8} color="#FFFFFF" fontWeight="bold">
                    3
                  </Text>
                </View>
              </View>
            ),
            onPress: () => {
              console.log('Notifications pressed');
              router.push('/notifications');
            },
          },
          {
            icon: <AppIcons.settings size={20} />,
            onPress: () => {
              console.log('Settings pressed');
              router.push('/settings');
            },
          },
        ]}
        backgroundColor={correctBg}
      />

      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: 16,
          paddingBottom: 80 + 16 + insets.bottom // Tab bar height + padding + safe area
        }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Active Service Sessions */}
        <DashboardCard
          title="Active Service Sessions"
          subtitle="Updated 2s ago"
          onViewAll={handleViewAllSessions}
          showRefresh
          onRefresh={refetchSessions}
        >
          {sessionsLoading ? (
            <View alignItems="center" paddingVertical="$lg">
              <LoadingSpinner size="small" />
            </View>
          ) : sessionsError || !activeSessions?.length ? (
            <EmptyState
              icon={<AppIcons.checkmark size={24} color="#10B981" />}
              title="No Active Sessions"
              subtitle="There are no active service sessions at the moment"
            />
          ) : (
            <View>
              {/* Sessions content would go here */}
              <Text>Active sessions: {activeSessions?.length || 0}</Text>
            </View>
          )}
        </DashboardCard>

        {/* Pending Appointments */}
        <DashboardCard
          title="Pending Appointments"
          subtitle="Updated 2s ago"
          onViewAll={handleViewAllAppointments}
          showRefresh
          onRefresh={refetchAppointments}
        >
          {appointmentsLoading ? (
            <View alignItems="center" paddingVertical="$lg">
              <LoadingSpinner size="small" />
            </View>
          ) : appointmentsError || !pendingAppointments?.length ? (
            <EmptyState
              icon={<AppIcons.checkmark size={24} color="#10B981" />}
              title="All Caught Up!"
              subtitle="No pending appointments to review"
            />
          ) : (
            <View>
              {/* Appointments content would go here */}
              <Text>Pending appointments: {pendingAppointments?.length || 0}</Text>
            </View>
          )}
        </DashboardCard>

        {/* Today's Schedule */}
        <DashboardCard
          title="Today's Schedule"
          subtitle="Updated 0s ago"
          onViewAll={handleViewAllSchedule}
        >
          {todayLoading ? (
            <View alignItems="center" paddingVertical="$lg">
              <LoadingSpinner size="small" />
            </View>
          ) : todayError || !todayAppointments?.length ? (
            <EmptyState
              icon={<AppIcons.calendar size={24} color="#3B82F6" />}
              title="No Appointments Today"
              subtitle="Your schedule is clear for today"
            />
          ) : (
            <View>
              {/* Today's schedule content would go here */}
              <Text>Today&apos;s appointments: {todayAppointments?.length || 0}</Text>
            </View>
          )}
        </DashboardCard>
      </ScrollView>
    </View>
  );
};

