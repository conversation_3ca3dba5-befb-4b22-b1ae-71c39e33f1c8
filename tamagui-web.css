/* Changa Font Faces */
@font-face {
  font-family: 'Changa-Light';
  src: url('./assets/fonts/Changa/static/Changa-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa-Regular';
  src: url('./assets/fonts/Changa/static/Changa-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa-Medium';
  src: url('./assets/fonts/Changa/static/Changa-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa-SemiBold';
  src: url('./assets/fonts/Changa/static/Changa-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa-Bold';
  src: url('./assets/fonts/Changa/static/Changa-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa-ExtraBold';
  src: url('./assets/fonts/Changa/static/Changa-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Global Changa font family */
@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa';
  src: url('./assets/fonts/Changa/static/Changa-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

html,
body,
#__next {
  width: 100%;
  /* To smooth any scrolling behavior */
  -webkit-overflow-scrolling: touch;
  margin: 0px;
  padding: 0px;
  /* Allows content to fill the viewport and go beyond the bottom */
  min-height: 100%;
}

#__next {
  flex-shrink: 0;
  flex-basis: auto;
  flex-direction: column;
  flex-grow: 1;
  display: flex;
  flex: 1;
}

html {
  scroll-behavior: smooth;
  /* Prevent text size change on orientation change https://gist.github.com/tfausak/2222823#file-ios-8-web-app-html-L138 */
  -webkit-text-size-adjust: 100%;
  height: 100%;
}

body {
  display: flex;
  /* Allows you to scroll below the viewport; default value is visible */
  overflow-y: auto;
  overscroll-behavior-y: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -ms-overflow-style: scrollbar;
  font-family: 'Changa-Regular', 'Changa', system-ui, -apple-system, sans-serif !important;
}

/* Ensure all text elements use Changa by default */
* {
  font-family: 'Changa-Regular', 'Changa', system-ui, -apple-system, sans-serif;
}

/* Override any font_unset classes */
.font_unset {
  font-family: 'Changa-Regular', 'Changa', system-ui, -apple-system, sans-serif !important;
}
