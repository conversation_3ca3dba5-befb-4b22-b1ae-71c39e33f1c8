ninja: Entering directory `D:\reactnative adscloud\dalti_provider_2\android\app\.cxx\Debug\84s5v5j1\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/2] Linking CXX shared library "D:\reactnative adscloud\dalti_provider_2\android\app\build\intermediates\cxx\Debug\84s5v5j1\obj\armeabi-v7a\libappmodules.so"
