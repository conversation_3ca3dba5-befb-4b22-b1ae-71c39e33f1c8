import {
    AntDesign,
    Entypo,
    EvilIcons,
    Feather,
    FontAwesome,
    FontAwesome5,
    Foundation,
    Ionicons,
    MaterialCommunityIcons,
    MaterialIcons,
    Octicons,
    SimpleLineIcons,
} from '@expo/vector-icons'
import { View } from '@tamagui/core'
import React from 'react'
import { Image } from 'react-native'

// Icon Library Types
export type IconLibrary =
  | 'AntDesign'
  | 'Entypo'
  | 'EvilIcons'
  | 'Feather'
  | 'FontAwesome'
  | 'FontAwesome5'
  | 'Foundation'
  | 'Ionicons'
  | 'MaterialCommunityIcons'
  | 'MaterialIcons'
  | 'Octicons'
  | 'SimpleLineIcons'

// Enhanced Icon Props
export interface IconProps {
  name: string
  library?: IconLibrary
  size?: number
  color?: string
  style?: any
}

// Legacy interface for backward compatibility
interface LegacyIconProps {
  size?: number
  color?: string
}

// Universal Icon Component
export const Icon: React.FC<IconProps> = ({
  name,
  library = 'Ionicons',
  size = 24,
  color = '#666',
  style
}) => {
  const iconProps = { name: name as any, size, color, style }

  switch (library) {
    case 'AntDesign':
      return <AntDesign {...iconProps} />
    case 'Entypo':
      return <Entypo {...iconProps} />
    case 'EvilIcons':
      return <EvilIcons {...iconProps} />
    case 'Feather':
      return <Feather {...iconProps} />
    case 'FontAwesome':
      return <FontAwesome {...iconProps} />
    case 'FontAwesome5':
      return <FontAwesome5 {...iconProps} />
    case 'Foundation':
      return <Foundation {...iconProps} />
    case 'Ionicons':
      return <Ionicons {...iconProps} />
    case 'MaterialCommunityIcons':
      return <MaterialCommunityIcons {...iconProps} />
    case 'MaterialIcons':
      return <MaterialIcons {...iconProps} />
    case 'Octicons':
      return <Octicons {...iconProps} />
    case 'SimpleLineIcons':
      return <SimpleLineIcons {...iconProps} />
    default:
      return <Ionicons {...iconProps} />
  }
}

// Commonly Used App Icons with predefined settings
export const AppIcons = {
  // Navigation & UI
  home: (props?: Partial<IconProps>) => <Icon name="home" library="Ionicons" {...props} />,
  menu: (props?: Partial<IconProps>) => <Icon name="menu" library="Ionicons" {...props} />,
  ellipsisVertical: (props?: Partial<IconProps>) => <Icon name="ellipsis-vertical" library="Ionicons" {...props} />,
  back: (props?: Partial<IconProps>) => <Icon name="arrow-back" library="Ionicons" {...props} />,
  close: (props?: Partial<IconProps>) => <Icon name="close" library="Ionicons" {...props} />,
  search: (props?: Partial<IconProps>) => <Icon name="search" library="Ionicons" {...props} />,
  filter: (props?: Partial<IconProps>) => <Icon name="filter" library="Ionicons" {...props} />,
  chevronDown: (props?: Partial<IconProps>) => <Icon name="chevron-down" library="Ionicons" {...props} />,
  refresh: (props?: Partial<IconProps>) => <Icon name="refresh" library="Ionicons" {...props} />,
  settings: (props?: Partial<IconProps>) => <Icon name="settings" library="Ionicons" {...props} />,
  notifications: (props?: Partial<IconProps>) => <Icon name="notifications" library="Ionicons" {...props} />,

  // Tab Bar Icons
  grid: (props?: Partial<IconProps>) => <Icon name="grid" library="Ionicons" {...props} />,
  qrCode: (props?: Partial<IconProps>) => <Icon name="qr-code" library="Ionicons" {...props} />,
  people: (props?: Partial<IconProps>) => <Icon name="people" library="Ionicons" {...props} />,
  bookmarks: (props?: Partial<IconProps>) => <Icon name="bookmarks" library="Ionicons" {...props} />,

  // User & Profile
  person: (props?: Partial<IconProps>) => <Icon name="person" library="Ionicons" {...props} />,
  personCircle: (props?: Partial<IconProps>) => <Icon name="person-circle" library="Ionicons" {...props} />,
  personAdd: (props?: Partial<IconProps>) => <Icon name="person-add" library="Ionicons" {...props} />,
  edit: (props?: Partial<IconProps>) => <Icon name="pencil" library="Ionicons" {...props} />,

  // Business & Categories
  business: (props?: Partial<IconProps>) => <Icon name="business" library="Ionicons" {...props} />,
  category: (props?: Partial<IconProps>) => <Icon name="list" library="Ionicons" {...props} />,
  lock: (props?: Partial<IconProps>) => <Icon name="lock-closed" library="Ionicons" {...props} />,

  // Communication
  call: (props?: Partial<IconProps>) => <Icon name="call" library="Ionicons" {...props} />,
  mail: (props?: Partial<IconProps>) => <Icon name="mail" library="Ionicons" {...props} />,
  chatbubble: (props?: Partial<IconProps>) => <Icon name="chatbubble" library="Ionicons" {...props} />,

  // Actions
  add: (props?: Partial<IconProps>) => <Icon name="add" library="Ionicons" {...props} />,
  remove: (props?: Partial<IconProps>) => <Icon name="remove" library="Ionicons" {...props} />,
  save: (props?: Partial<IconProps>) => <Icon name="save" library="Ionicons" {...props} />,
  share: (props?: Partial<IconProps>) => <Icon name="share" library="Ionicons" {...props} />,
  download: (props?: Partial<IconProps>) => <Icon name="download" library="Ionicons" {...props} />,

  // Status & Feedback
  checkmark: (props?: Partial<IconProps>) => <Icon name="checkmark" library="Ionicons" {...props} />,
  checkmarkCircle: (props?: Partial<IconProps>) => <Icon name="checkmark-circle" library="Ionicons" {...props} />,
  warning: (props?: Partial<IconProps>) => <Icon name="warning" library="Ionicons" {...props} />,
  alert: (props?: Partial<IconProps>) => <Icon name="alert-circle" library="Ionicons" {...props} />,

  // Business & Services
  calendar: (props?: Partial<IconProps>) => <Icon name="calendar" library="Ionicons" {...props} />,
  time: (props?: Partial<IconProps>) => <Icon name="time" library="Ionicons" {...props} />,
  location: (props?: Partial<IconProps>) => <Icon name="location" library="Ionicons" {...props} />,
  card: (props?: Partial<IconProps>) => <Icon name="card" library="Ionicons" {...props} />,
  document: (props?: Partial<IconProps>) => <Icon name="document-text" library="Ionicons" {...props} />,

  // Security
  unlock: (props?: Partial<IconProps>) => <Icon name="lock-open" library="Ionicons" {...props} />,
  key: (props?: Partial<IconProps>) => <Icon name="key" library="Ionicons" {...props} />,

  // Theme & Display
  sun: (props?: Partial<IconProps>) => <Icon name="sunny" library="Ionicons" {...props} />,
  moon: (props?: Partial<IconProps>) => <Icon name="moon" library="Ionicons" {...props} />,
  eye: (props?: Partial<IconProps>) => <Icon name="eye" library="Ionicons" {...props} />,
  eyeOff: (props?: Partial<IconProps>) => <Icon name="eye-off" library="Ionicons" {...props} />,

  // Additional icons for settings
  globe: (props?: Partial<IconProps>) => <Icon name="globe" library="Ionicons" {...props} />,
  helpCircle: (props?: Partial<IconProps>) => <Icon name="help-circle" library="Ionicons" {...props} />,
  logOut: (props?: Partial<IconProps>) => <Icon name="log-out" library="Ionicons" {...props} />,

  // Rating & Reviews
  star: (props?: Partial<IconProps>) => <Icon name="star" library="Ionicons" {...props} />,

  // Media & Camera
  camera: (props?: Partial<IconProps>) => <Icon name="camera" library="Ionicons" {...props} />,
}

// Legacy icon components for backward compatibility
export const GlobeIcon: React.FC<LegacyIconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 2}
    backgroundColor={color}
    opacity={0.7}
  />
)

export const MoonIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 2}
    backgroundColor={color}
    opacity={0.7}
  />
)

export const UserIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 4}
    backgroundColor={color}
    opacity={0.5}
  />
)

export const LockIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 6}
    backgroundColor={color}
    opacity={0.5}
  />
)

export const EyeIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 4}
    backgroundColor={color}
    opacity={0.5}
  />
)

export const EyeOffIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 4}
    backgroundColor={color}
    opacity={0.3}
  />
)

export const MailIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 6}
    backgroundColor={color}
    opacity={0.5}
  />
)

export const ArrowLeftIcon: React.FC<IconProps> = ({ size = 20, color = '#666' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 8}
    backgroundColor={color}
    opacity={0.7}
  />
)

// Dalti Logo Component
export const DaltiLogo: React.FC<{ size?: number; variant?: 'default' | 'white' | 'dark' }> = ({
  size = 80,
  variant = 'default'
}) => {
  // Determine which logo to use based on variant
  const getLogoSource = () => {
    switch (variant) {
      case 'white':
        return require('../../assets/images/logo-white.png')
      case 'dark':
        return require('../../assets/images/logo-dark.png')
      default:
        return require('../../assets/images/logo-white.png') // Default to white logo
    }
  }

  return (
    <View alignItems="center">
      <Image
        source={getLogoSource()}
        resizeMode="contain"
        style={{
          width: size,
          height: size,
        }}
      />
    </View>
  )
}

// Dalti Logo Icon Component (for smaller displays like headers)
export const DaltiLogoIcon: React.FC<{ size?: number; variant?: 'default' | 'white' | 'dark' }> = ({
  size = 40,
  variant = 'default'
}) => {
  // Determine which logo icon to use based on variant
  const getLogoIconSource = () => {
    switch (variant) {
      case 'white':
        return require('../../assets/images/logo-icon-white.png')
      case 'dark':
        return require('../../assets/images/logo-icon-dark.png')
      default:
        return require('../../assets/images/logo-icon-white.png') // Default to white icon
    }
  }

  return (
    <View alignItems="center">
      <Image
        source={getLogoIconSource()}
        resizeMode="contain"
        style={{
          width: size,
          height: size,
        }}
      />
    </View>
  )
}
