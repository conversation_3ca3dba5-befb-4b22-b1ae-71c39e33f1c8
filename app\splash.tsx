import { Text, View } from '@tamagui/core'
import { useRouter } from 'expo-router'
import React, { useEffect } from 'react'
import { DaltiLogo } from '../components/ui/Icons'
import { useTheme } from '../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../theme/themes'

export default function SplashScreen() {
  const router = useRouter()
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      router.replace('/welcome')
    }, 2500)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <View
      flex={1}
      backgroundColor={currentTheme.background}
      alignItems="center"
      justifyContent="center"
      gap="$xl"
    >
      {/* Logo */}
      <DaltiLogo size={120} variant={isDark ? 'dark' : 'white'} />
      
      {/* Brand Name */}
      <View alignItems="center" gap="$sm">
        <Text
          fontSize="$headlineLarge"
          fontWeight="bold"
          color="$primary"
          textAlign="center"
        >
          dalti
        </Text>
        <Text
          fontSize="$bodyMedium"
          color="$colorSecondary"
          textAlign="center"
        >
          Provider
        </Text>
      </View>

      {/* Loading indicator */}
      <View marginTop="$4xl">
        <View
          width={40}
          height={4}
          backgroundColor="$borderColor"
          borderRadius="$full"
          overflow="hidden"
        >
          <View
            width="60%"
            height="100%"
            backgroundColor="$primary"
            borderRadius="$full"
            // Add animation here if needed
          />
        </View>
      </View>
    </View>
  )
}
