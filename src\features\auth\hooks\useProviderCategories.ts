import { useState, useEffect } from 'react';
import { AuthService } from '../services/auth.service';
import { ProviderCategory } from '../../../types/api.types';

export interface UseProviderCategoriesReturn {
  categories: ProviderCategory[];
  parentCategories: ProviderCategory[];
  childCategories: ProviderCategory[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getChildCategories: (parentId: number) => ProviderCategory[];
}

export const useProviderCategories = (): UseProviderCategoriesReturn => {
  const [categories, setCategories] = useState<ProviderCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const authService = AuthService.getInstance();
      const response = await authService.getProviderCategories();

      if (response.success && response.data) {
        setCategories(response.data);
      } else {
        setError(response.message || 'Failed to fetch categories');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch categories');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Separate parent and child categories
  const parentCategories = categories.filter(cat => cat.parentId === null);
  const childCategories = categories.filter(cat => cat.parentId !== null);

  // Get child categories for a specific parent
  const getChildCategories = (parentId: number): ProviderCategory[] => {
    return categories.filter(cat => cat.parentId === parentId);
  };

  return {
    categories,
    parentCategories,
    childCategories,
    isLoading,
    error,
    refetch: fetchCategories,
    getChildCategories,
  };
};
