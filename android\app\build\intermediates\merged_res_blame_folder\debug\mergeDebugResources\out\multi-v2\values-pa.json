{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3569,3671,3771,3872,3974,4072,13710", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "3564,3666,3766,3867,3969,4067,4196,13806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,247,369,498,630,778,905,1058,1157,1297,1434", "endColumns": "104,86,121,128,131,147,126,152,98,139,136,129", "endOffsets": "155,242,364,493,625,773,900,1053,1152,1292,1429,1559"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4581,4791,5408,5530,5659,5791,5939,6066,6219,6318,6458,6595", "endColumns": "104,86,121,128,131,147,126,152,98,139,136,129", "endOffsets": "4681,4873,5525,5654,5786,5934,6061,6214,6313,6453,6590,6720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,203,274,343,423,490,557,631,707,790,869,937,1015,1098,1172,1256,1344,1419,1490,1561,1647,1716,1790,1859", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,198,269,338,418,485,552,626,702,785,864,932,1010,1093,1167,1251,1339,1414,1485,1556,1642,1711,1785,1854,1927"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,4504,6725,6865,6934,7073,8345,8412,8486,12456,12539,12618,12686,13078,13161,13235,13319,13407,13482,13553,13624,13811,13880,13954,14023", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3041,4576,6791,6929,7009,7135,8407,8481,8557,12534,12613,12681,12759,13156,13230,13314,13402,13477,13548,13619,13705,13875,13949,14018,14091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,12764", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,12839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4686,5090,5191,5305", "endColumns": "104,100,113,102", "endOffsets": "4786,5186,5300,5403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1420,1505,1568,1630,1688,1752,1813,1878,1944,1999,2053,2167,2225,2285,2339,2409,2536,2617,2707,2806,2903,2982,3117,3193,3270,3399,3483,3564,3619,3674,3740,3809,3886,3957,4036,4104,4180,4250,4315,4417,4512,4585,4679,4772,4846,4915,5009,5065,5148,5215,5299,5387,5449,5513,5576,5643,5740,5846,5937,6039,6138,6235,6294,6353,6430,6515,6591", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,98,96,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1415,1500,1563,1625,1683,1747,1808,1873,1939,1994,2048,2162,2220,2280,2334,2404,2531,2612,2702,2801,2898,2977,3112,3188,3265,3394,3478,3559,3614,3669,3735,3804,3881,3952,4031,4099,4175,4245,4310,4412,4507,4580,4674,4767,4841,4910,5004,5060,5143,5210,5294,5382,5444,5508,5571,5638,5735,5841,5932,6034,6133,6230,6289,6348,6425,6510,6586,6659"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3046,3123,3202,3283,3382,4201,4309,4421,4878,4934,4998,6796,7014,7140,7229,7314,7377,7439,7497,7561,7622,7687,7753,7808,7862,7976,8034,8094,8148,8218,8562,8643,8733,8832,8929,9008,9143,9219,9296,9425,9509,9590,9645,9700,9766,9835,9912,9983,10062,10130,10206,10276,10341,10443,10538,10611,10705,10798,10872,10941,11035,11091,11174,11241,11325,11413,11475,11539,11602,11669,11766,11872,11963,12065,12164,12261,12320,12379,12844,12929,13005", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,98,96,58,58,76,84,75,72", "endOffsets": "308,3118,3197,3278,3377,3466,4304,4416,4499,4929,4993,5085,6860,7068,7224,7309,7372,7434,7492,7556,7617,7682,7748,7803,7857,7971,8029,8089,8143,8213,8340,8638,8728,8827,8924,9003,9138,9214,9291,9420,9504,9585,9640,9695,9761,9830,9907,9978,10057,10125,10201,10271,10336,10438,10533,10606,10700,10793,10867,10936,11030,11086,11169,11236,11320,11408,11470,11534,11597,11664,11761,11867,11958,12060,12159,12256,12315,12374,12451,12924,13000,13073"}}]}]}