import { ScrollView, Text, View } from '@tamagui/core'
import React from 'react'
import { useTheme } from '../../theme/DaltiThemeProvider'
import { darkTheme, lightTheme } from '../../theme/themes'

export const IconShowcase: React.FC = () => {
  const { isDark } = useTheme()
  const currentTheme = isDark ? darkTheme : lightTheme

  return (
    <View flex={1} backgroundColor={currentTheme.background} padding="$lg">
      {/* Header */}
      <View marginBottom="$lg">
        <Text
          fontSize="$headlineLarge"
          fontWeight="bold"
          color={currentTheme.color}
          marginBottom="$sm"
        >
          Icon Library Showcase
        </Text>
        <Text
          fontSize="$bodyMedium"
          color={currentTheme.colorSecondary}
          marginBottom="$lg"
        >
          Explore available icons from @expo/vector-icons
        </Text>
      </View>

      {/* Simple Test Section */}
      <View marginBottom="$lg">
        <Text
          fontSize="$headlineMedium"
          fontWeight="bold"
          color={currentTheme.color}
          marginBottom="$md"
        >
          Icons Library Test
        </Text>
        <Text
          fontSize="$bodyMedium"
          color={currentTheme.colorSecondary}
          marginBottom="$md"
        >
          Testing the icons library functionality.
        </Text>
        
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            flexDirection="row"
            flexWrap="wrap"
            gap="$md"
            paddingBottom="$xl"
          >
            {['home', 'menu', 'back', 'close'].map((name) => (
              <View key={name} alignItems="center" padding="$sm" width="23%">
                <View
                  padding="$md"
                  borderRadius="$md"
                  backgroundColor={currentTheme.surfaceContainer}
                  marginBottom="$xs"
                  alignItems="center"
                  justifyContent="center"
                  aspectRatio={1}
                  borderWidth={1}
                  borderColor={currentTheme.borderColor}
                >
                  <Text fontSize="$bodySmall" color={currentTheme.primary}>
                    {name}
                  </Text>
                </View>
                <Text
                  fontSize="$bodySmall"
                  color={currentTheme.colorSecondary}
                  textAlign="center"
                  numberOfLines={2}
                  fontWeight="500"
                >
                  {name}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Usage Example */}
      <View 
        padding="$md" 
        backgroundColor={currentTheme.surfaceContainer}
        borderRadius="$md"
        borderWidth={1}
        borderColor={currentTheme.borderColor}
      >
        <Text
          fontSize="$headlineSmall"
          fontWeight="bold"
          color={currentTheme.color}
          marginBottom="$sm"
        >
          Usage Example
        </Text>
        <Text
          fontSize="$bodyMedium"
          color={currentTheme.colorSecondary}
          lineHeight={20}
        >
          Import and use icons in your components:
        </Text>
        <View 
          padding="$sm" 
          backgroundColor={currentTheme.background}
          borderRadius="$sm"
          marginTop="$sm"
        >
          <Text
            fontSize="$bodySmall"
            color={currentTheme.color}
            fontFamily="monospace"
          >
            {`import { AppIcons } from './Icons'

// Use predefined icons
<AppIcons.home size={24} color="#257587" />
<AppIcons.person size={20} color="#666" />

// Or use the universal Icon component
import { Icon } from './Icons'
<Icon name="home" library="Ionicons" size={24} />`}
          </Text>
        </View>
      </View>
    </View>
  )
}
