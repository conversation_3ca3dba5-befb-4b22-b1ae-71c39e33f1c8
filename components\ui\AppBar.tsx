import React from 'react'
import { Pressable } from 'react-native'
import { Text, View, useTheme as useTamaguiTheme } from 'tamagui'

export interface AppBarIconProps {
  icon: React.ReactNode
  onPress?: () => void
  disabled?: boolean
}

export interface AppBarProps {
  /** Optional title displayed next to the start icon */
  title?: string
  /** Icon displayed on the start (left) side */
  startIcon?: AppBarIconProps
  /** Icon displayed on the end (right) side */
  endIcon?: AppBarIconProps
  /** Additional icons on the end side (array of icons) */
  endIcons?: AppBarIconProps[]
  /** Custom background color */
  backgroundColor?: string
  /** Custom title color */
  titleColor?: string
  /** Custom container style */
  containerStyle?: any
}

export const AppBar: React.FC<AppBarProps> = ({
  title,
  startIcon,
  endIcon,
  endIcons = [],
  backgroundColor,
  titleColor,
  containerStyle,
}) => {
  const tamaguiTheme = useTamaguiTheme()
  
  // Use theme values directly with fallback
  const isDarkMode = tamaguiTheme.color?.val === '#F8F9FA' // Dark theme has light text
  
  // Default colors based on theme
  const defaultBgColor = backgroundColor || (isDarkMode ? '#0F1419' : '#FFFFFF')
  const defaultTitleColor = titleColor || (isDarkMode ? '#F8F9FA' : '#2C3E50')
  const defaultIconColor = isDarkMode ? '#FFFFFF' : '#7F8C8D' // White in dark mode, gray in light mode
  const iconContainerBg = isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'

  // Combine end icons (single endIcon + endIcons array)
  const allEndIcons = [
    ...(endIcon ? [endIcon] : []),
    ...endIcons
  ]

  // Helper function to apply default color to icons if not specified
  const applyDefaultIconColor = (icon: React.ReactNode): React.ReactNode => {
    if (React.isValidElement(icon)) {
      // Check if the icon already has a color prop
      const hasColor = icon.props && (icon.props.color !== undefined)

      if (!hasColor) {
        // Clone the element with default color
        return React.cloneElement(icon as React.ReactElement, {
          color: defaultIconColor
        })
      }
    }
    return icon
  }

  const renderIconContainer = (iconProps: AppBarIconProps, key: string) => {
    const { icon, onPress, disabled = false } = iconProps
    const iconWithDefaultColor = applyDefaultIconColor(icon)

    if (!onPress) {
      // Non-clickable icon container
      return (
        <View
          key={key}
          width={40}
          height={40}
          borderRadius={12}
          backgroundColor={iconContainerBg}
          alignItems="center"
          justifyContent="center"
          opacity={disabled ? 0.5 : 1}
        >
          {iconWithDefaultColor}
        </View>
      )
    }

    // Clickable icon container
    return (
      <Pressable
        key={key}
        onPress={disabled ? undefined : onPress}
        disabled={disabled}
        style={({ pressed }) => ({
          width: 40,
          height: 40,
          borderRadius: 12,
          backgroundColor: iconContainerBg,
          alignItems: 'center',
          justifyContent: 'center',
          opacity: disabled ? 0.5 : pressed ? 0.7 : 1,
        })}
      >
        {iconWithDefaultColor}
      </Pressable>
    )
  }

  return (
    <View
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      paddingHorizontal="$lg"
      paddingVertical="$lg"
      minHeight={64}
      backgroundColor={defaultBgColor}
      {...containerStyle}
    >
      {/* Start section */}
      <View flexDirection="row" alignItems="center" flex={1}>
        {/* Start icon */}
        {startIcon && (
          <View marginRight={title ? "$md" : 0}>
            {renderIconContainer(startIcon, 'start-icon')}
          </View>
        )}
        
        {/* Title */}
        {title && (
          <Text
            fontSize="$headlineSmall"
            fontWeight="600"
            color={defaultTitleColor}
            numberOfLines={1}
            flex={1}
          >
            {title}
          </Text>
        )}
      </View>

      {/* End section */}
      {allEndIcons.length > 0 && (
        <View flexDirection="row" gap="$sm">
          {allEndIcons.map((iconProps, index) => 
            renderIconContainer(iconProps, `end-icon-${index}`)
          )}
        </View>
      )}
    </View>
  )
}

// Export types for external use
export type { AppBarIconProps, AppBarProps }

