import { Text, View } from '@tamagui/core'
import React from 'react'
import { Pressable, PressableProps } from 'react-native'

export interface ButtonProps extends Omit<PressableProps, 'style'> {
  title: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  containerStyle?: any
}

export const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  leftIcon,
  rightIcon,
  disabled,
  containerStyle,
  onPress,
  ...pressableProps
}) => {


  // Size configurations
  const sizeConfig = {
    sm: {
      height: '$buttonSm',
      paddingHorizontal: '$lg',
      fontSize: '$bodySmall',
    },
    md: {
      height: '$buttonMd',
      paddingHorizontal: '$xl',
      fontSize: '$bodyMedium',
    },
    lg: {
      height: '$buttonLg',
      paddingHorizontal: '$2xl',
      fontSize: '$bodyLarge',
    },
  }

  // Variant configurations
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: '$primary',
          borderColor: '$primary',
          borderWidth: 1,
          textColor: '$colorOnPrimary',
        }
      case 'secondary':
        return {
          backgroundColor: '$secondary',
          borderColor: '$secondary',
          borderWidth: 1,
          textColor: '$colorOnSecondary',
        }
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: '$borderColor',
          borderWidth: 1,
          textColor: '$color',
        }
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
          textColor: '$primary',
        }
      default:
        return {
          backgroundColor: '$primary',
          borderColor: '$primary',
          borderWidth: 1,
          textColor: '$colorOnPrimary',
        }
    }
  }

  const currentSize = sizeConfig[size]
  const variantStyles = getVariantStyles()
  const isDisabled = disabled || loading

  return (
    <Pressable
      onPress={onPress}
      disabled={isDisabled}
      style={({ pressed }) => [
        {
          opacity: pressed ? 0.8 : isDisabled ? 0.6 : 1,
        },
        containerStyle,
      ]}
      {...pressableProps}
    >
      <View
        height={currentSize.height}
        paddingHorizontal={currentSize.paddingHorizontal}
        backgroundColor={variantStyles.backgroundColor}
        borderColor={variantStyles.borderColor}
        borderWidth={variantStyles.borderWidth}
        borderRadius="$button"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap="$sm"
        width={fullWidth ? '100%' : undefined}
      >
        {leftIcon && !loading && leftIcon}
        
        {loading ? (
          <View
            width={16}
            height={16}
            borderRadius={8}
            borderWidth={2}
            borderColor={variantStyles.textColor}
            borderTopColor="transparent"
            // Add rotation animation here if needed
          />
        ) : (
          <Text
            fontSize={currentSize.fontSize}
            fontWeight="semibold"
            color={variantStyles.textColor}
            textAlign="center"
          >
            {title}
          </Text>
        )}
        
        {rightIcon && !loading && rightIcon}
      </View>
    </Pressable>
  )
}
