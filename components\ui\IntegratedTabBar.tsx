import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { Text, View } from 'tamagui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const IntegratedTabBar: React.FC<BottomTabBarProps> = ({ 
  state, 
  descriptors, 
  navigation 
}) => {
  const { isDark } = useTheme();
  const insets = useSafeAreaInsets();

  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const centerButtonBg = isDark ? '#257587' : '#15424E';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  const handleCenterPress = () => {
    console.log('QR Code scanner pressed');
    // TODO: Navigate to QR scanner or open modal
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: tabBarBg,
          borderTopColor: borderColor,
          paddingBottom: insets.bottom,
        },
      ]}
    >
      {/* Main tab bar with integrated center button */}
      <View style={styles.tabBar}>
        {/* Left tabs */}
        <View style={styles.tabSection}>
          {state.routes.slice(0, 2).map((route, index) => {
            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <Pressable key={route.key} onPress={onPress} style={styles.tab}>
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={isFocused ? 22 : 20}
                />
                <Text
                  fontSize={isFocused ? 12 : 11}
                  color={isFocused ? activeColor : inactiveColor}
                  fontWeight={isFocused ? '600' : '400'}
                  marginTop={2}
                >
                  {getTabLabel(route.name)}
                </Text>
              </Pressable>
            );
          })}
        </View>

        {/* Center button with notch design */}
        <View style={styles.centerSection}>
          {/* Notch background */}
          <View
            style={[
              styles.notchBackground,
              { backgroundColor: tabBarBg }
            ]}
          />
          
          {/* Center button */}
          <Pressable
            onPress={handleCenterPress}
            style={[
              styles.centerButton,
              { backgroundColor: centerButtonBg }
            ]}
          >
            <AppIcons.qrCode size={26} color="#FFFFFF" />
          </Pressable>
        </View>

        {/* Right tabs */}
        <View style={styles.tabSection}>
          {state.routes.slice(2, 4).map((route, index) => {
            const actualIndex = index + 2;
            const isFocused = state.index === actualIndex;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <Pressable key={route.key} onPress={onPress} style={styles.tab}>
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={isFocused ? 22 : 20}
                />
                <Text
                  fontSize={isFocused ? 12 : 11}
                  color={isFocused ? activeColor : inactiveColor}
                  fontWeight={isFocused ? '600' : '400'}
                  marginTop={2}
                >
                  {getTabLabel(route.name)}
                </Text>
              </Pressable>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  tabBar: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 70,
    paddingHorizontal: 10,
  },
  tabSection: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 10,
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    minHeight: 50,
  },
  centerSection: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 80,
    height: 90,
    marginBottom: -20,
  },
  notchBackground: {
    position: 'absolute',
    bottom: 20,
    width: 90,
    height: 45,
    borderTopLeftRadius: 45,
    borderTopRightRadius: 45,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
