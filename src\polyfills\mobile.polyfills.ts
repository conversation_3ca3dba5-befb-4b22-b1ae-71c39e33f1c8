// Mobile polyfills to prevent web-specific API errors
// This file provides safe fallbacks for web APIs that don't exist in React Native

import { Platform } from 'react-native';

// Only apply polyfills on mobile platforms
if (Platform.OS !== 'web') {
  // Document polyfill
  if (typeof document === 'undefined') {
    (global as any).document = {
      body: {
        className: '',
        style: {},
      },
      createElement: () => ({}),
      getElementById: () => null,
      querySelector: () => null,
      querySelectorAll: () => [],
      addEventListener: () => {},
      removeEventListener: () => {},
    };
  }

  // Window polyfill
  if (typeof window === 'undefined') {
    (global as any).window = {
      location: {
        href: 'react-native://app',
        origin: 'react-native://app',
      },
      navigator: {
        userAgent: `React Native ${Platform.OS}`,
      },
      addEventListener: () => {},
      removeEventListener: () => {},
      getComputedStyle: () => ({}),
    };
  }

  // Navigator polyfill
  if (typeof navigator === 'undefined') {
    (global as any).navigator = {
      userAgent: `React Native ${Platform.OS}`,
      platform: Platform.OS,
    };
  }

  // Console polyfill (ensure it exists)
  if (typeof console === 'undefined') {
    (global as any).console = {
      log: () => {},
      warn: () => {},
      error: () => {},
      info: () => {},
      debug: () => {},
    };
  }
}

export {};
