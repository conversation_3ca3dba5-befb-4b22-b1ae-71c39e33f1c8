# Dalti Provider App

A professional React Native application for service providers built with Expo, featuring custom theming, authentication flows, and modern UI components.

## 🚀 Features

### 🎨 **Custom Theme System**
- **Light/Dark Mode**: Seamless theme switching with custom color palettes
- **Professional Color Scheme**: Custom teal-based branding with optimal contrast
- **Theme-Aware Components**: All UI elements adapt to the current theme
- **Tamagui Integration**: Powerful design system with design tokens

### 🔐 **Authentication System**
- **Login Screen**: Email/phone and password authentication
- **Registration**: Complete signup flow with validation
- **Forgot Password**: Password recovery functionality
- **Input Validation**: Real-time form validation and error handling

### 🎯 **UI/UX Excellence**
- **Custom Logo Integration**: Theme-aware logo variants (white/dark)
- **Professional Typography**: Changa font family integration
- **Responsive Design**: Optimized for all screen sizes
- **Smooth Animations**: Polished user interactions
- **Accessibility**: High contrast ratios and proper focus states

### 📱 **Screen Architecture**
- **Splash Screen**: Branded loading experience
- **Welcome Screen**: Onboarding with call-to-action buttons
- **Authentication Flows**: Login, register, and password recovery
- **Navigation**: Expo Router with proper screen transitions

## 🛠️ Tech Stack

- **Framework**: React Native with Expo
- **UI Library**: Tamagui (Cross-platform design system)
- **Navigation**: Expo Router
- **Styling**: Custom design tokens and theme system
- **Typography**: Google Fonts (Changa family)
- **State Management**: React Context for theme management
- **Development**: TypeScript for type safety

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Zedster07/dalti-provider-app.git
   cd dalti-provider-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app

## 🎨 Theme Configuration

The app features a sophisticated theme system with custom color palettes:

### Light Mode Colors
- **Screen Background**: `#15424E` (Deep teal-green)
- **Content Background**: `#FAFAFA` (Clean off-white)
- **Primary**: `#15424E` (Brand teal)
- **Logo**: White variant for contrast

### Dark Mode Colors
- **Screen Background**: `#257587` (Sophisticated teal-blue)
- **Content Background**: `#1C2127` (Rich dark surface)
- **Primary**: `#257587` (Brand teal)
- **Logo**: Dark variant for contrast

## 📁 Project Structure

```
dalti-provider-app/
├── app/                    # Screen components (Expo Router)
│   ├── splash.tsx         # Splash screen
│   ├── welcome.tsx        # Welcome/onboarding
│   ├── login.tsx          # Login screen
│   ├── register.tsx       # Registration screen
│   └── forgot-password.tsx # Password recovery
├── components/ui/          # Reusable UI components
│   ├── Button.tsx         # Custom button component
│   ├── Input.tsx          # Input field with variants
│   ├── PasswordInput.tsx  # Password input with toggle
│   ├── Icons.tsx          # Logo and icon components
│   └── ScreenHeader.tsx   # Header with theme toggle
├── theme/                  # Theme system
│   ├── DaltiThemeProvider.tsx # Theme context provider
│   ├── themes.ts          # Light/dark theme definitions
│   ├── tokens.ts          # Design tokens
│   └── utils.ts           # Theme utilities
├── assets/
│   ├── fonts/             # Custom font files
│   └── images/            # Logo variants and assets
└── tamagui.config.ts      # Tamagui configuration
```

## 🎯 Key Components

### Authentication Screens
- **Splash**: Branded loading with logo and progress indicator
- **Welcome**: Onboarding with login/signup options
- **Login**: Email/phone and password authentication
- **Register**: Complete signup with validation
- **Forgot Password**: Password recovery flow

### UI Components
- **DaltiLogo**: Theme-aware logo component with variants
- **Button**: Custom button with multiple variants and sizes
- **Input**: Enhanced input fields with borders and validation
- **PasswordInput**: Password field with visibility toggle
- **ScreenHeader**: Header with theme toggle functionality

## 🚀 Development

### Adding New Screens
1. Create new file in `app/` directory
2. Export default React component
3. Use theme context for consistent styling

### Customizing Theme
1. Modify colors in `theme/themes.ts`
2. Update design tokens in `theme/tokens.ts`
3. Components automatically adapt to changes

### Adding UI Components
1. Create component in `components/ui/`
2. Use Tamagui components and design tokens
3. Export from `components/ui/index.ts`

## 📱 Screenshots

The app features a professional design with:
- Clean, modern interface
- Consistent branding across all screens
- Smooth theme transitions
- Professional typography and spacing
- Optimized for both light and dark modes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Tamagui**: For the excellent cross-platform design system
- **Expo**: For the amazing React Native development platform
- **Google Fonts**: For the beautiful Changa typography
- **React Native Community**: For the robust ecosystem

---

**Built with ❤️ for service providers**
