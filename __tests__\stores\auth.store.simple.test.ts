import { useAuthStore } from '../../src/stores/auth.store';

// Mock the dependencies
jest.mock('../../src/services/api/api.client', () => ({
  apiClient: {
    post: jest.fn(),
    setAuthToken: jest.fn(),
    removeAuthToken: jest.fn(),
  },
}));

jest.mock('../../src/services/storage/storage.service', () => ({
  storageService: {
    getAuthToken: jest.fn(),
    setAuthToken: jest.fn(),
    getUserData: jest.fn(),
    setUserData: jest.fn(),
    getProviderData: jest.fn(),
    setProviderData: jest.fn(),
    clearAllData: jest.fn(),
  },
}));

describe('AuthStore - Simple Tests', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useAuthStore.setState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      provider: null,
      error: null,
      isInitialized: false,
    });

    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct default state', () => {
      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.user).toBe(null);
      expect(state.provider).toBe(null);
      expect(state.error).toBe(null);
      expect(state.isInitialized).toBe(false);
    });
  });

  describe('state mutations', () => {
    it('should update authentication state', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'provider',
      };

      useAuthStore.setState({
        isAuthenticated: true,
        user: mockUser,
      });

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(mockUser);
    });

    it('should clear error state', () => {
      useAuthStore.setState({ error: 'Some error' });
      
      const { clearError } = useAuthStore.getState();
      clearError();

      const state = useAuthStore.getState();
      expect(state.error).toBe(null);
    });

    it('should update user data', () => {
      const initialUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'provider',
      };

      useAuthStore.setState({ user: initialUser });

      const { updateUser } = useAuthStore.getState();
      updateUser({ firstName: 'Jane' });

      const state = useAuthStore.getState();
      expect(state.user?.firstName).toBe('Jane');
      expect(state.user?.lastName).toBe('Doe'); // Should preserve other fields
    });

    it('should update provider data', () => {
      const initialProvider = {
        id: 1,
        userId: '1',
        providerCategoryId: 1,
        title: 'Test Provider',
        phone: '+**********',
        isSetupComplete: false,
      };

      useAuthStore.setState({ provider: initialProvider });

      const { updateProvider } = useAuthStore.getState();
      updateProvider({ isSetupComplete: true });

      const state = useAuthStore.getState();
      expect(state.provider?.isSetupComplete).toBe(true);
      expect(state.provider?.title).toBe('Test Provider'); // Should preserve other fields
    });
  });

  describe('action functions', () => {
    it('should have all required action functions', () => {
      const state = useAuthStore.getState();

      expect(typeof state.initialize).toBe('function');
      expect(typeof state.login).toBe('function');
      expect(typeof state.register).toBe('function');
      expect(typeof state.verifyOTP).toBe('function');
      expect(typeof state.logout).toBe('function');
      expect(typeof state.refreshToken).toBe('function');
      expect(typeof state.clearError).toBe('function');
      expect(typeof state.updateUser).toBe('function');
      expect(typeof state.updateProvider).toBe('function');
    });

    it('should handle loading states', () => {
      useAuthStore.setState({ isLoading: true });
      
      const state = useAuthStore.getState();
      expect(state.isLoading).toBe(true);
    });

    it('should handle error states', () => {
      const errorMessage = 'Test error';
      useAuthStore.setState({ error: errorMessage });
      
      const state = useAuthStore.getState();
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('store subscription', () => {
    it('should notify subscribers of state changes', () => {
      const mockSubscriber = jest.fn();
      
      const unsubscribe = useAuthStore.subscribe(mockSubscriber);
      
      useAuthStore.setState({ isAuthenticated: true });
      
      expect(mockSubscriber).toHaveBeenCalled();
      
      unsubscribe();
    });

    it('should allow multiple subscribers', () => {
      const mockSubscriber1 = jest.fn();
      const mockSubscriber2 = jest.fn();
      
      const unsubscribe1 = useAuthStore.subscribe(mockSubscriber1);
      const unsubscribe2 = useAuthStore.subscribe(mockSubscriber2);
      
      useAuthStore.setState({ isLoading: true });
      
      expect(mockSubscriber1).toHaveBeenCalled();
      expect(mockSubscriber2).toHaveBeenCalled();
      
      unsubscribe1();
      unsubscribe2();
    });
  });

  describe('state persistence', () => {
    it('should have persistence configuration', () => {
      // This test verifies that the store is configured with persistence
      // The actual persistence behavior is handled by Zustand middleware
      const state = useAuthStore.getState();
      
      // These are the fields that should be persisted
      expect(state.hasOwnProperty('isAuthenticated')).toBe(true);
      expect(state.hasOwnProperty('user')).toBe(true);
      expect(state.hasOwnProperty('provider')).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle updateUser when no user exists', () => {
      useAuthStore.setState({ user: null });

      const { updateUser } = useAuthStore.getState();
      updateUser({ firstName: 'Jane' });

      const state = useAuthStore.getState();
      expect(state.user).toBe(null); // Should remain null
    });

    it('should handle updateProvider when no provider exists', () => {
      useAuthStore.setState({ provider: null });

      const { updateProvider } = useAuthStore.getState();
      updateProvider({ isSetupComplete: true });

      const state = useAuthStore.getState();
      expect(state.provider).toBe(null); // Should remain null
    });

    it('should handle multiple rapid state changes', () => {
      useAuthStore.setState({ isLoading: true });
      useAuthStore.setState({ isLoading: false });
      useAuthStore.setState({ error: 'Error' });
      useAuthStore.setState({ error: null });

      const state = useAuthStore.getState();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe(null);
    });
  });
});
