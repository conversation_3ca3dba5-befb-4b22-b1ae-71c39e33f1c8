import React, { createContext, useContext, useEffect, useState } from 'react'
import { TamaguiProvider, TamaguiProviderProps } from '@tamagui/core'
import { useColorScheme } from 'react-native'
import tamaguiConfig from '../tamagui.config'

// Theme context for additional theme utilities
interface ThemeContextType {
  theme: 'light' | 'dark'
  toggleTheme: () => void
  setTheme: (theme: 'light' | 'dark') => void
  isDark: boolean
  isLight: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a DaltiThemeProvider')
  }
  return context
}

// Props for DaltiThemeProvider
interface DaltiThemeProviderProps extends Omit<TamaguiProviderProps, 'config' | 'defaultTheme'> {
  children: React.ReactNode
  defaultTheme?: 'light' | 'dark' | 'system'
  forcedTheme?: 'light' | 'dark'
}

export const DaltiThemeProvider: React.FC<DaltiThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
  forcedTheme,
  ...tamaguiProps
}) => {
  const systemColorScheme = useColorScheme()
  const [theme, setThemeState] = useState<'light' | 'dark'>(() => {
    if (forcedTheme) return forcedTheme
    if (defaultTheme === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light'
    }
    return defaultTheme
  })

  // Update theme when system color scheme changes (if using system theme)
  useEffect(() => {
    if (!forcedTheme && defaultTheme === 'system') {
      setThemeState(systemColorScheme === 'dark' ? 'dark' : 'light')
    }
  }, [systemColorScheme, defaultTheme, forcedTheme])

  const toggleTheme = () => {
    if (!forcedTheme) {
      setThemeState(prev => prev === 'light' ? 'dark' : 'light')
    }
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    if (!forcedTheme) {
      setThemeState(newTheme)
    }
  }

  const themeContextValue: ThemeContextType = {
    theme,
    toggleTheme,
    setTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  }

  return (
    <ThemeContext.Provider value={themeContextValue}>
      <TamaguiProvider
        config={tamaguiConfig}
        defaultTheme={theme}
        {...tamaguiProps}
      >
        {children}
      </TamaguiProvider>
    </ThemeContext.Provider>
  )
}

// Utility hooks for common theme operations
export const useIsDark = () => {
  const { isDark } = useTheme()
  return isDark
}

export const useIsLight = () => {
  const { isLight } = useTheme()
  return isLight
}

export const useThemeToggle = () => {
  const { toggleTheme } = useTheme()
  return toggleTheme
}

// Theme-aware component wrapper
export const withTheme = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const theme = useTheme()
    return <Component {...props} ref={ref} theme={theme} />
  })
}

export default DaltiThemeProvider
