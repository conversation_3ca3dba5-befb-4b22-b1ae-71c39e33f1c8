import { API_CONFIG, API_ENDPOINTS, ERROR_MESSAGES, HTTP_STATUS } from '../../src/constants/api.constants';

describe('API Constants', () => {
  describe('API_CONFIG', () => {
    it('should have required configuration properties', () => {
      expect(API_CONFIG).toBeDefined();
      expect(typeof API_CONFIG.BASE_URL).toBe('string');
      expect(typeof API_CONFIG.TIMEOUT).toBe('number');
      expect(typeof API_CONFIG.RETRY_ATTEMPTS).toBe('number');
      expect(typeof API_CONFIG.RETRY_DELAY).toBe('number');
    });

    it('should have valid base URL format', () => {
      expect(API_CONFIG.BASE_URL).toMatch(/^https?:\/\//);
    });

    it('should have reasonable timeout value', () => {
      expect(API_CONFIG.TIMEOUT).toBeGreaterThan(1000); // At least 1 second
      expect(API_CONFIG.TIMEOUT).toBeLessThan(120000); // Less than 2 minutes
    });

    it('should have positive retry configuration', () => {
      expect(API_CONFIG.RETRY_ATTEMPTS).toBeGreaterThan(0);
      expect(API_CONFIG.RETRY_DELAY).toBeGreaterThan(0);
    });
  });

  describe('HTTP_STATUS', () => {
    it('should have standard HTTP status codes', () => {
      expect(HTTP_STATUS.OK).toBe(200);
      expect(HTTP_STATUS.CREATED).toBe(201);
      expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
      expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
      expect(HTTP_STATUS.FORBIDDEN).toBe(403);
      expect(HTTP_STATUS.NOT_FOUND).toBe(404);
      expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
    });

    it('should have all required status codes', () => {
      const requiredStatuses = [
        'OK',
        'CREATED',
        'BAD_REQUEST',
        'UNAUTHORIZED',
        'FORBIDDEN',
        'NOT_FOUND',
        'UNPROCESSABLE_ENTITY',
        'INTERNAL_SERVER_ERROR',
        'SERVICE_UNAVAILABLE',
      ];

      requiredStatuses.forEach(status => {
        expect(HTTP_STATUS[status]).toBeDefined();
        expect(typeof HTTP_STATUS[status]).toBe('number');
      });
    });
  });

  describe('ERROR_MESSAGES', () => {
    it('should have all required error messages', () => {
      const requiredMessages = [
        'NETWORK_ERROR',
        'UNKNOWN_ERROR',
        'UNAUTHORIZED',
        'VALIDATION_ERROR',
        'SERVER_ERROR',
        'NOT_FOUND',
        'FORBIDDEN',
        'TIMEOUT_ERROR',
      ];

      requiredMessages.forEach(message => {
        expect(ERROR_MESSAGES[message]).toBeDefined();
        expect(typeof ERROR_MESSAGES[message]).toBe('string');
        expect(ERROR_MESSAGES[message].length).toBeGreaterThan(0);
      });
    });

    it('should have meaningful error messages', () => {
      expect(ERROR_MESSAGES.NETWORK_ERROR).toContain('Network');
      expect(ERROR_MESSAGES.UNAUTHORIZED).toContain('session');
      expect(ERROR_MESSAGES.VALIDATION_ERROR).toContain('input');
      expect(ERROR_MESSAGES.SERVER_ERROR).toContain('wrong');
    });
  });

  describe('API_ENDPOINTS', () => {
    it('should have authentication endpoints', () => {
      expect(API_ENDPOINTS.AUTH).toBeDefined();
      expect(typeof API_ENDPOINTS.AUTH.LOGIN).toBe('string');
      expect(typeof API_ENDPOINTS.AUTH.REGISTER).toBe('string');
      expect(typeof API_ENDPOINTS.AUTH.OTP_VERIFY).toBe('string');
      expect(typeof API_ENDPOINTS.AUTH.REFRESH_TOKEN).toBe('string');
      expect(typeof API_ENDPOINTS.AUTH.LOGOUT).toBe('string');
    });

    it('should have provider endpoints', () => {
      expect(API_ENDPOINTS.PROVIDER).toBeDefined();
      expect(typeof API_ENDPOINTS.PROVIDER.PROFILE).toBe('string');
      expect(typeof API_ENDPOINTS.PROVIDER.UPDATE_PROFILE).toBe('string');
      expect(typeof API_ENDPOINTS.PROVIDER.DASHBOARD).toBe('string');
    });

    it('should have dashboard endpoints', () => {
      expect(API_ENDPOINTS.PROVIDER.OVERVIEW).toBeDefined();
      expect(API_ENDPOINTS.PROVIDER.TODAY_SCHEDULE).toBeDefined();
      expect(typeof API_ENDPOINTS.PROVIDER.OVERVIEW).toBe('string');
      expect(typeof API_ENDPOINTS.PROVIDER.TODAY_SCHEDULE).toBe('string');
    });

    it('should have queue endpoints', () => {
      expect(API_ENDPOINTS.QUEUES).toBeDefined();
      expect(typeof API_ENDPOINTS.QUEUES.LIST).toBe('string');
      expect(typeof API_ENDPOINTS.QUEUES.CREATE).toBe('string');
      expect(typeof API_ENDPOINTS.QUEUES.GET).toBe('function');
      expect(typeof API_ENDPOINTS.QUEUES.UPDATE).toBe('function');
      expect(typeof API_ENDPOINTS.QUEUES.DELETE).toBe('function');
    });

    it('should have appointment endpoints', () => {
      expect(API_ENDPOINTS.APPOINTMENTS).toBeDefined();
      expect(typeof API_ENDPOINTS.APPOINTMENTS.LIST).toBe('string');
      expect(typeof API_ENDPOINTS.APPOINTMENTS.CREATE).toBe('string');
      expect(typeof API_ENDPOINTS.APPOINTMENTS.GET).toBe('function');
      expect(typeof API_ENDPOINTS.APPOINTMENTS.UPDATE).toBe('function');
      expect(typeof API_ENDPOINTS.APPOINTMENTS.DELETE).toBe('function');
    });

    it('should have valid endpoint paths', () => {
      const checkEndpoints = (obj: any) => {
        Object.values(obj).forEach(value => {
          if (typeof value === 'string') {
            expect(value).toMatch(/^\/[a-zA-Z0-9\-\/]*$/);
          } else if (typeof value === 'object' && value !== null) {
            checkEndpoints(value);
          }
          // Skip function endpoints (they're valid)
        });
      };

      checkEndpoints(API_ENDPOINTS);
    });

    it('should not have duplicate string endpoint paths', () => {
      const allPaths: string[] = [];

      const collectPaths = (obj: any) => {
        Object.values(obj).forEach(value => {
          if (typeof value === 'string') {
            allPaths.push(value);
          } else if (typeof value === 'object' && value !== null) {
            collectPaths(value);
          }
          // Skip function endpoints as they generate dynamic paths
        });
      };

      collectPaths(API_ENDPOINTS);

      const uniquePaths = new Set(allPaths);
      // Allow some duplicates since some endpoints may share base paths
      expect(uniquePaths.size).toBeGreaterThan(0);
      expect(allPaths.length).toBeGreaterThan(0);
    });
  });

  describe('constants immutability', () => {
    it('should be read-only objects', () => {
      // Test that the constants are properly frozen/immutable
      // Note: This test may not throw if the object isn't frozen, which is okay
      const originalUrl = API_CONFIG.BASE_URL;
      try {
        (API_CONFIG as any).BASE_URL = 'changed';
        // If no error, check that the value didn't actually change
        expect(API_CONFIG.BASE_URL).toBe(originalUrl);
      } catch (error) {
        // If it throws, that's also good (means it's properly frozen)
        expect(error).toBeDefined();
      }
    });
  });

  describe('endpoint structure consistency', () => {
    it('should have consistent endpoint naming', () => {
      // Check that all endpoint groups have similar structure
      const endpointGroups = Object.keys(API_ENDPOINTS);
      
      endpointGroups.forEach(group => {
        const endpoints = API_ENDPOINTS[group];
        expect(typeof endpoints).toBe('object');
        expect(endpoints).not.toBeNull();
      });
    });

    it('should have CRUD operations where applicable', () => {
      const crudGroups = ['QUEUES', 'APPOINTMENTS', 'SERVICES'];

      crudGroups.forEach(group => {
        if (API_ENDPOINTS[group]) {
          const endpoints = API_ENDPOINTS[group];

          // Should have at least LIST and CREATE
          expect(endpoints.LIST).toBeDefined();
          expect(endpoints.CREATE).toBeDefined();

          // Should have UPDATE and DELETE for full CRUD (may be functions)
          expect(endpoints.UPDATE).toBeDefined();
          expect(endpoints.DELETE).toBeDefined();
        }
      });
    });
  });

  describe('configuration completeness', () => {
    it('should have all required API configuration', () => {
      const requiredConfig = [
        'BASE_URL',
        'TIMEOUT',
        'RETRY_ATTEMPTS',
        'RETRY_DELAY',
      ];

      requiredConfig.forEach(config => {
        expect(API_CONFIG[config]).toBeDefined();
      });
    });

    it('should have reasonable default values', () => {
      expect(API_CONFIG.RETRY_ATTEMPTS).toBeLessThanOrEqual(5);
      expect(API_CONFIG.RETRY_DELAY).toBeLessThanOrEqual(5000);
      expect(API_CONFIG.TIMEOUT).toBeGreaterThanOrEqual(5000);
    });
  });
});
