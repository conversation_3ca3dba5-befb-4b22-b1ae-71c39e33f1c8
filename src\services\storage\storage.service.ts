import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../../constants/api.constants';

// Simple storage implementation using only AsyncStorage for maximum compatibility
class SimpleStorage {
  private cache: Map<string, string> = new Map();

  async set(key: string, value: string): Promise<void> {
    this.cache.set(key, value);
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('Error setting item in AsyncStorage:', error);
    }
  }

  async getString(key: string): Promise<string | null> {
    // Try cache first
    const cached = this.cache.get(key);
    if (cached) return cached;

    // Fallback to AsyncStorage
    try {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        this.cache.set(key, value);
      }
      return value;
    } catch (error) {
      console.error('Error getting item from AsyncStorage:', error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing item from AsyncStorage:', error);
    }
  }

  async clearAll(): Promise<void> {
    this.cache.clear();
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing AsyncStorage:', error);
    }
  }

  getAllKeys(): string[] {
    return Array.from(this.cache.keys());
  }
}

// Initialize simple storage
const storage = new SimpleStorage();

export class StorageService {
  private static instance: StorageService;

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  // Secure storage for sensitive data (tokens, passwords)
  async setSecureItem(key: string, value: string): Promise<void> {
    // Always use AsyncStorage as primary storage to avoid Keychain issues
    try {
      await AsyncStorage.setItem(`secure_${key}`, value);
    } catch (error) {
      console.error('Error storing secure item in AsyncStorage:', error);
      throw error;
    }
  }

  async getSecureItem(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(`secure_${key}`);
    } catch (error) {
      console.error('Error retrieving secure item from AsyncStorage:', error);
      return null;
    }
  }

  async removeSecureItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`secure_${key}`);
    } catch (error) {
      console.error('Error removing secure item from AsyncStorage:', error);
    }
  }

  // Fast storage for non-sensitive data
  setItem(key: string, value: any): void {
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    storage.set(key, stringValue).catch(error => {
      console.error('Error storing item:', error);
    });
  }

  getItem(key: string): string | null {
    // Return cached value immediately for sync compatibility
    const cached = storage.cache.get(key);
    if (cached) return cached;

    // Trigger async load for next time
    storage.getString(key).catch(error => {
      console.error('Error retrieving item:', error);
    });

    return null;
  }

  getObject<T>(key: string): T | null {
    const cached = storage.cache.get(key);
    if (cached) {
      try {
        return JSON.parse(cached);
      } catch (error) {
        console.error('Error parsing cached object:', error);
        return null;
      }
    }

    // Trigger async load for next time
    storage.getString(key).catch(error => {
      console.error('Error retrieving object:', error);
    });

    return null;
  }

  removeItem(key: string): void {
    storage.delete(key).catch(error => {
      console.error('Error removing item:', error);
    });
  }

  // Async versions for better compatibility
  async getItemAsync(key: string): Promise<string | null> {
    return await storage.getString(key);
  }

  async getObjectAsync<T>(key: string): Promise<T | null> {
    try {
      const value = await storage.getString(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Error retrieving object:', error);
      return null;
    }
  }

  async setObjectAsync<T>(key: string, value: T): Promise<void> {
    try {
      await storage.set(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error storing object:', error);
    }
  }

  // Authentication token management
  async setAuthToken(token: string): Promise<void> {
    console.log('💾 Storage: Saving auth token...', { tokenLength: token?.length || 0 });
    await this.setSecureItem(STORAGE_KEYS.AUTH_TOKEN, token);
    console.log('✅ Storage: Auth token saved');
  }

  async getAuthToken(): Promise<string | null> {
    console.log('🔍 Storage: Retrieving auth token...');
    const token = await this.getSecureItem(STORAGE_KEYS.AUTH_TOKEN);
    console.log('📤 Storage: Auth token retrieved', { hasToken: !!token, tokenLength: token?.length || 0 });
    return token;
  }

  async setRefreshToken(token: string): Promise<void> {
    await this.setSecureItem(STORAGE_KEYS.REFRESH_TOKEN, token);
  }

  async getRefreshToken(): Promise<string | null> {
    return await this.getSecureItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  async clearAuthTokens(): Promise<void> {
    await this.removeSecureItem(STORAGE_KEYS.AUTH_TOKEN);
    await this.removeSecureItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  // User data management
  setUserData(userData: any): void {
    console.log('💾 Storage: Saving user data...', { hasUserData: !!userData });
    this.setItem(STORAGE_KEYS.USER_DATA, userData);
    console.log('✅ Storage: User data saved');
  }

  async getUserData<T>(): Promise<T | null> {
    console.log('🔍 Storage: Retrieving user data...');
    const userData = await this.getObjectAsync<T>(STORAGE_KEYS.USER_DATA);
    console.log('📤 Storage: User data retrieved', { hasUserData: !!userData });
    return userData;
  }

  setProviderData(providerData: any): void {
    console.log('💾 Storage: Saving provider data...', { hasProviderData: !!providerData });
    this.setItem(STORAGE_KEYS.PROVIDER_DATA, providerData);
    console.log('✅ Storage: Provider data saved');
  }

  async getProviderData<T>(): Promise<T | null> {
    console.log('🔍 Storage: Retrieving provider data...');
    const providerData = await this.getObjectAsync<T>(STORAGE_KEYS.PROVIDER_DATA);
    console.log('📤 Storage: Provider data retrieved', { hasProviderData: !!providerData });
    return providerData;
  }

  // App preferences
  setThemePreference(theme: 'light' | 'dark' | 'system'): void {
    this.setItem(STORAGE_KEYS.THEME_PREFERENCE, theme);
  }

  getThemePreference(): 'light' | 'dark' | 'system' | null {
    return this.getItem(STORAGE_KEYS.THEME_PREFERENCE) as 'light' | 'dark' | 'system' | null;
  }

  setLanguagePreference(language: string): void {
    this.setItem(STORAGE_KEYS.LANGUAGE_PREFERENCE, language);
  }

  getLanguagePreference(): string | null {
    return this.getItem(STORAGE_KEYS.LANGUAGE_PREFERENCE);
  }

  setNotificationSettings(settings: any): void {
    this.setItem(STORAGE_KEYS.NOTIFICATION_SETTINGS, settings);
  }

  getNotificationSettings<T>(): T | null {
    return this.getObject<T>(STORAGE_KEYS.NOTIFICATION_SETTINGS);
  }

  setOnboardingCompleted(completed: boolean): void {
    this.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, completed.toString());
  }

  getOnboardingCompleted(): boolean {
    const value = this.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
    return value === 'true';
  }

  // Clear all data (logout)
  async clearAllData(): Promise<void> {
    try {
      // Clear secure storage
      await this.clearAuthTokens();

      // Clear storage
      await storage.clearAll();
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }

  // Development helpers
  getAllKeys(): string[] {
    return storage.getAllKeys();
  }

  logAllData(): void {
    if (__DEV__) {
      const keys = this.getAllKeys();
      console.log('=== Storage Data ===');
      keys.forEach(key => {
        const value = this.getItem(key);
        console.log(`${key}:`, value);
      });
      console.log('===================');
    }
  }
}

// Export singleton instance
export const storageService = StorageService.getInstance();
