// This file must be loaded before any other modules to polyfill import.meta

// Polyfill import.meta for web environments
(function() {
  'use strict';
  
  // Create import.meta object if it doesn't exist
  if (typeof import === 'undefined') {
    // Define import globally
    if (typeof window !== 'undefined') {
      window.import = {};
    }
    if (typeof globalThis !== 'undefined') {
      globalThis.import = {};
    }
    if (typeof global !== 'undefined') {
      global.import = {};
    }
  }
  
  // Define import.meta
  const importMeta = {
    url: typeof window !== 'undefined' ? window.location.href : 'file:///',
    env: typeof process !== 'undefined' ? process.env : {}
  };
  
  // Set import.meta on all global objects
  if (typeof window !== 'undefined') {
    if (!window.import) window.import = {};
    window.import.meta = importMeta;
  }
  
  if (typeof globalThis !== 'undefined') {
    if (!globalThis.import) globalThis.import = {};
    globalThis.import.meta = importMeta;
  }
  
  if (typeof global !== 'undefined') {
    if (!global.import) global.import = {};
    global.import.meta = importMeta;
  }
  
  // Also define it directly on the global scope
  if (typeof window !== 'undefined') {
    window['import.meta'] = importMeta;
  }
  
  if (typeof globalThis !== 'undefined') {
    globalThis['import.meta'] = importMeta;
  }
  
  console.log('✅ import.meta polyfill loaded');
})();
