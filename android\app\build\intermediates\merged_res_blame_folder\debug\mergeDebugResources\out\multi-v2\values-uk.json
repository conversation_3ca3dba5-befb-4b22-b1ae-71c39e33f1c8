{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,289,363,451,523,590,666,745,833,919,991,1072,1157,1233,1315,1398,1475,1548,1621,1706,1780,1860,1930", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,210,284,358,446,518,585,661,740,828,914,986,1067,1152,1228,1310,1393,1470,1543,1616,1701,1775,1855,1925,2010"}, "to": {"startLines": "35,51,71,73,74,76,94,95,96,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3153,4692,6910,7050,7124,7275,8575,8642,8718,12784,12872,12958,13030,13431,13516,13592,13674,13757,13834,13907,13980,14166,14240,14320,14390", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "3222,4773,6979,7119,7207,7342,8637,8713,8792,12867,12953,13025,13106,13511,13587,13669,13752,13829,13902,13975,14060,14235,14315,14385,14470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3670,3770,3872,3973,4074,4179,4284,14065", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3765,3867,3968,4069,4174,4279,4392,14161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1135,1217,1308,1401,1496,1590,1690,1783,1878,1973,2064,2155,2254,2360,2466,2564,2671,2778,2883,3053,13111", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1130,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,3148,13188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "53,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4885,5318,5425,5545", "endColumns": "109,106,119,107", "endOffsets": "4990,5420,5540,5648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1553,1641,1703,1770,1828,1899,1958,2029,2103,2162,2216,2330,2390,2453,2507,2580,2699,2785,2861,2952,3033,3116,3255,3340,3427,3561,3649,3727,3784,3835,3901,3973,4049,4120,4203,4276,4353,4435,4509,4618,4708,4787,4878,4974,5048,5129,5224,5278,5360,5426,5513,5599,5661,5725,5788,5861,5968,6078,6176,6282,6387,6488,6549,6604,6686,6771,6847", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,81,87,61,66,57,70,58,70,73,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,133,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,104,100,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1548,1636,1698,1765,1823,1894,1953,2024,2098,2157,2211,2325,2385,2448,2502,2575,2694,2780,2856,2947,3028,3111,3250,3335,3422,3556,3644,3722,3779,3830,3896,3968,4044,4115,4198,4271,4348,4430,4504,4613,4703,4782,4873,4969,5043,5124,5219,5273,5355,5421,5508,5594,5656,5720,5783,5856,5963,6073,6171,6277,6382,6483,6544,6599,6681,6766,6842,6919"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,55,56,57,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3227,3305,3383,3471,3579,4397,4493,4609,5088,5160,5227,6984,7212,7347,7429,7517,7579,7646,7704,7775,7834,7905,7979,8038,8092,8206,8266,8329,8383,8456,8797,8883,8959,9050,9131,9214,9353,9438,9525,9659,9747,9825,9882,9933,9999,10071,10147,10218,10301,10374,10451,10533,10607,10716,10806,10885,10976,11072,11146,11227,11322,11376,11458,11524,11611,11697,11759,11823,11886,11959,12066,12176,12274,12380,12485,12586,12647,12702,13193,13278,13354", "endLines": "7,36,37,38,39,40,48,49,50,55,56,57,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,81,87,61,66,57,70,58,70,73,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,133,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,104,100,60,54,81,84,75,76", "endOffsets": "419,3300,3378,3466,3574,3665,4488,4604,4687,5155,5222,5313,7045,7270,7424,7512,7574,7641,7699,7770,7829,7900,7974,8033,8087,8201,8261,8324,8378,8451,8570,8878,8954,9045,9126,9209,9348,9433,9520,9654,9742,9820,9877,9928,9994,10066,10142,10213,10296,10369,10446,10528,10602,10711,10801,10880,10971,11067,11141,11222,11317,11371,11453,11519,11606,11692,11754,11818,11881,11954,12061,12171,12269,12375,12480,12581,12642,12697,12779,13273,13349,13426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "52,54,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4778,4995,5653,5780,5911,6052,6163,6290,6424,6523,6653,6785", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "4880,5083,5775,5906,6047,6158,6285,6419,6518,6648,6780,6905"}}]}]}