import { Text, View } from '@tamagui/core'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { useEffect, useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { Input } from '../../../../components/ui/Input'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { useAuthStore } from '../../../stores/auth.store'

export default function OTPVerificationScreen() {
  const router = useRouter()
  const params = useLocalSearchParams()
  const { isDark } = useTheme()

  const insets = useSafeAreaInsets()
  const [otp, setOtp] = useState('')
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Auth store
  const { verifyOTP, register, isLoading, error, clearError } = useAuthStore()

  // Get registration data from params
  const registrationData = {
    email: params.email as string,
    firstName: params.firstName as string,
    lastName: params.lastName as string,
    password: params.password as string,
    providerCategoryId: parseInt(params.providerCategoryId as string),
    businessName: params.businessName as string,
    phone: params.phone as string,
  }

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#257587',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#15424E',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  // Countdown timer for resend OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [countdown])

  const handleBack = () => {
    router.back()
  }

  const handleVerifyOTP = async () => {
    if (!otp.trim() || otp.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP')
      return
    }

    clearError()

    try {
      const success = await verifyOTP({
        otp: otp.trim(),
        identifier: registrationData.email,
        password: registrationData.password,
        firstName: registrationData.firstName,
        lastName: registrationData.lastName,
        providerCategoryId: registrationData.providerCategoryId,
        businessName: registrationData.businessName,
        phone: registrationData.phone,
        email: registrationData.email,
      })

      if (success) {
        Alert.alert('Success', 'Account verified successfully!', [
          {
            text: 'OK',
            onPress: () => router.replace('/(tabs)'),
          },
        ])
      } else {
        Alert.alert('Verification Failed', error || 'Invalid OTP')
      }
    } catch {
      Alert.alert('Error', 'An unexpected error occurred')
    }
  }

  const handleResendOTP = async () => {
    if (!canResend) return

    try {
      const success = await register(registrationData)
      if (success) {
        setCountdown(60)
        setCanResend(false)
        Alert.alert('Success', 'OTP sent successfully!')
      } else {
        Alert.alert('Error', 'Failed to resend OTP')
      }
    } catch {
      Alert.alert('Error', 'Failed to resend OTP')
    }
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Verify Your Email
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
            >
              We sent a 6-digit code to {registrationData.email}
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* OTP Input */}
              <Input
                label="Verification Code"
                placeholder="Enter 6-digit code"
                value={otp}
                onChangeText={setOtp}
                keyboardType="number-pad"
                maxLength={6}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
                textAlign="center"
                fontSize="$headlineSmall"
                letterSpacing={4}
              />

              {/* Verify Button */}
              <Button
                title="Verify & Complete Registration"
                variant="primary"
                size="lg"
                fullWidth
                loading={isLoading}
                onPress={handleVerifyOTP}
                containerStyle={{ marginTop: 16 }}
              />

              {/* Resend OTP */}
              <View alignItems="center" marginTop="$lg">
                {canResend ? (
                  <Pressable onPress={handleResendOTP}>
                    <Text
                      fontSize="$bodyMedium"
                      color={authColors.linkColor}
                      fontWeight="semibold"
                    >
                      Resend Code
                    </Text>
                  </Pressable>
                ) : (
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.secondaryText}
                  >
                    Resend code in {countdown}s
                  </Text>
                )}
              </View>

              {/* Help text */}
              <View alignItems="center" marginTop="$lg">
                <Text
                  fontSize="$bodySmall"
                  color={authColors.secondaryText}
                  textAlign="center"
                >
                  Didn&apos;t receive the code? Check your spam folder or try resending.
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
