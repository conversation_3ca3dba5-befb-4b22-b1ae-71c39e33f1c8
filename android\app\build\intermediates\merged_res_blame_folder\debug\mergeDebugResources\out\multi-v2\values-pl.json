{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1511,1622,1686,1754,1808,1877,1939,2008,2081,2139,2193,2304,2365,2427,2481,2553,2682,2771,2850,2945,3030,3112,3261,3343,3426,3563,3650,3727,3781,3832,3898,3969,4045,4116,4199,4276,4354,4432,4508,4616,4706,4779,4874,4971,5043,5117,5217,5269,5354,5420,5508,5598,5660,5724,5787,5858,5965,6077,6176,6283,6380,6472,6530,6585,6661,6745,6822", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,77,110,63,67,53,68,61,68,72,57,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,96,91,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1506,1617,1681,1749,1803,1872,1934,2003,2076,2134,2188,2299,2360,2422,2476,2548,2677,2766,2845,2940,3025,3107,3256,3338,3421,3558,3645,3722,3776,3827,3893,3964,4040,4111,4194,4271,4349,4427,4503,4611,4701,4774,4869,4966,5038,5112,5212,5264,5349,5415,5503,5593,5655,5719,5782,5853,5960,6072,6171,6278,6375,6467,6525,6580,6656,6740,6817,6895"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,54,55,56,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,3481,4313,4398,4515,4968,5033,5097,6884,7102,7163,7241,7352,7416,7484,7538,7607,7669,7738,7811,7869,7923,8034,8095,8157,8211,8283,8567,8656,8735,8830,8915,8997,9146,9228,9311,9448,9535,9612,9666,9717,9783,9854,9930,10001,10084,10161,10239,10317,10393,10501,10591,10664,10759,10856,10928,11002,11102,11154,11239,11305,11393,11483,11545,11609,11672,11743,11850,11962,12061,12168,12265,12357,12415,12470,12962,13046,13123", "endLines": "7,35,36,37,38,39,47,48,49,54,55,56,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,77,110,63,67,53,68,61,68,72,57,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,96,91,57,54,75,83,76,77", "endOffsets": "431,3218,3293,3372,3476,3571,4393,4510,4592,5028,5092,5173,6943,7158,7236,7347,7411,7479,7533,7602,7664,7733,7806,7864,7918,8029,8090,8152,8206,8278,8407,8651,8730,8825,8910,8992,9141,9223,9306,9443,9530,9607,9661,9712,9778,9849,9925,9996,10079,10156,10234,10312,10388,10496,10586,10659,10754,10851,10923,10997,11097,11149,11234,11300,11388,11478,11540,11604,11667,11738,11845,11957,12056,12163,12260,12352,12410,12465,12541,13041,13118,13196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "51,53,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4674,4878,5496,5621,5759,5912,6039,6167,6314,6414,6548,6687", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "4773,4963,5616,5754,5907,6034,6162,6309,6409,6543,6682,6806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,12879", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,12957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "52,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4778,5178,5277,5392", "endColumns": "99,98,114,103", "endOffsets": "4873,5272,5387,5491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3673,3775,3873,3972,4086,4191,13833", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3668,3770,3868,3967,4081,4186,4308,13929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,205,274,359,435,514,597,692,762,847,933,1008,1090,1173,1251,1323,1393,1479,1557,1633,1707", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "127,200,269,354,430,509,592,687,757,842,928,1003,1085,1168,1246,1318,1388,1474,1552,1628,1702,1782"}, "to": {"startLines": "50,70,72,73,92,93,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4597,6811,6948,7017,8412,8488,12546,12629,12724,12794,13201,13287,13362,13444,13527,13605,13677,13747,13934,14012,14088,14162", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "4669,6879,7012,7097,8483,8562,12624,12719,12789,12874,13282,13357,13439,13522,13600,13672,13742,13828,14007,14083,14157,14237"}}]}]}