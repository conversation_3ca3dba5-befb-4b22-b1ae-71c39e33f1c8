import { useRouter } from 'expo-router'
import { useEffect } from 'react'
import { View } from 'react-native'

export default function Index() {
  const router = useRouter()

  useEffect(() => {
    // Use a small delay to ensure navigation is ready
    const timer = setTimeout(() => {
      router.replace('/splash')
    }, 100)

    return () => clearTimeout(timer)
  }, [router])

  // Return a minimal view while redirecting
  return <View style={{ flex: 1, backgroundColor: '#15424E' }} />
}
