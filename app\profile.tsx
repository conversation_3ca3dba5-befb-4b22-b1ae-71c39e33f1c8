import { useRouter } from 'expo-router'
import React from 'react'
import { Pressable } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { ScrollView, Text, View } from 'tamagui'
import { AppBar } from '../components/ui/AppBar'
import { AppIcons } from '../components/ui/Icons'
import { useTheme } from '../theme/DaltiThemeProvider'

// Profile Item Component
interface ProfileItemProps {
  icon: React.ReactNode
  title: string
  subtitle: string
  onPress?: () => void
  iconColor?: string
}

const ProfileItem: React.FC<ProfileItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  iconColor = '#257587'
}) => {
  const { isDark } = useTheme()

  const itemBg = isDark ? '#1C2127' : '#FFFFFF'
  const titleColor = isDark ? '#F8F9FA' : '#2C3E50'
  const subtitleColor = isDark ? '#B8BCC8' : '#7F8C8D'

  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => ({
        backgroundColor: itemBg,
        borderRadius: 8,
        marginBottom: 4,
        opacity: pressed ? 0.7 : 1,
      })}
    >
      <View
        flexDirection="row"
        alignItems="center"
        padding="$lg"
        gap="$md"
      >
        {/* Icon */}
        <View
          width={24}
          height={24}
          alignItems="center"
          justifyContent="center"
        >
          {icon}
        </View>

        {/* Content */}
        <View flex={1}>
          <Text
            fontSize="$bodyLarge"
            fontWeight="500"
            color={titleColor}
            marginBottom="$xs"
          >
            {title}
          </Text>
          <Text
            fontSize="$bodyMedium"
            color={subtitleColor}
          >
            {subtitle}
          </Text>
        </View>

        {/* Chevron */}
        <AppIcons.chevronDown
          size={20}
          color={subtitleColor}
          style={{ transform: [{ rotate: '-90deg' }] }}
        />
      </View>
    </Pressable>
  )
}

// Statistics Card Component
interface StatCardProps {
  icon: React.ReactNode
  value: string
  label: string
  iconColor?: string
}

interface StatCardProps {
  icon: React.ReactNode
  value: string
  label: string
  backgroundColor: string
  textColor?: string
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  value,
  label,
  backgroundColor,
  textColor = '#2C3E50'
}) => {
  return (
    <View
      style={{ backgroundColor }}
      borderRadius="$md"
      padding="$lg"
      alignItems="center"
      flex={1}
      gap="$sm"
    >
      {icon}
      <Text
        fontSize="$headlineMedium"
        fontWeight="bold"
        style={{ color: textColor }}
      >
        {value}
      </Text>
      <Text
        fontSize="$bodyMedium"
        style={{ color: textColor, opacity: 0.7 }}
      >
        {label}
      </Text>
    </View>
  )
}

// Section Header Component
interface SectionHeaderProps {
  title: string
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ title }) => {
  const { isDark } = useTheme()
  const sectionTitleColor = isDark ? '#257587' : '#257587'

  return (
    <Text
      fontSize="$titleMedium"
      fontWeight="600"
      color={sectionTitleColor}
      marginBottom="$sm"
      marginTop="$lg"
    >
      {title}
    </Text>
  )
}

export default function ProfileScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { isDark } = useTheme()

  const screenBg = isDark ? '#0F1419' : '#FAFAFA' // Theme-aware background

  const handleBack = () => {
    router.back()
  }

  const handleEdit = () => {
    console.log('Edit profile pressed')
    router.push('/edit-profile')
  }

  return (
    <View flex={1} backgroundColor={screenBg} style={{ paddingTop: insets.top }}>
      <AppBar
        title="Profile"
        startIcon={{
          icon: <AppIcons.back size={20} />,
          onPress: handleBack,
        }}
        endIcon={{
          icon: <AppIcons.edit size={20} />,
          onPress: handleEdit,
        }}
      />

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View padding="$lg" gap="$sm">
          {/* Profile Overview Section */}
          <SectionHeader title="Profile Overview" />

          <View
            backgroundColor={isDark ? "#1C2127" : "#FFFFFF"}
            borderRadius="$md"
            padding="$lg"
            gap="$md"
          >
            {/* Profile Info Row */}
            <View
              flexDirection="row"
              alignItems="center"
              gap="$md"
            >
              {/* Profile Avatar */}
              <View
                width={60}
                height={60}
                borderRadius={8}
                backgroundColor={isDark ? "#2A3038" : "#F5F5F5"}
                alignItems="center"
                justifyContent="center"
              >
                <AppIcons.person size={32} color={isDark ? "#B8BCC8" : "#7F8C8D"} />
              </View>

              {/* Profile Info */}
              <View flex={1}>
                <Text
                  fontSize="$headlineMedium"
                  fontWeight="bold"
                  color={isDark ? "#F8F9FA" : "#2C3E50"}
                  marginBottom="$xs"
                >
                  clinique du testeur 2
                </Text>
                <Text
                  fontSize="$bodyLarge"
                  color={isDark ? "#B8BCC8" : "#7F8C8D"}
                >
                  Doctor
                </Text>
              </View>
            </View>

            {/* About Me Section - Nested inside Profile Overview */}
            <View
              backgroundColor={isDark ? "#2A3038" : "#F5F5F5"}
              borderRadius={8}
              padding="$lg"
            >
              <View flexDirection="row" alignItems="center" gap="$sm" marginBottom="$md">
                <AppIcons.helpCircle size={20} color="#257587" />
                <Text
                  fontSize="$bodyLarge"
                  fontWeight="500"
                  color="#257587"
                >
                  About Me
                </Text>
              </View>
              <Text
                fontSize="$bodyMedium"
                color={isDark ? "#B8BCC8" : "#7F8C8D"}
                lineHeight={20}
              >
                une description du service
              </Text>
            </View>
          </View>

          {/* Statistics Section */}
          <SectionHeader title="Statistics" />

          <View flexDirection="row" gap="$md">
            <StatCard
              icon={<AppIcons.star size={24} color="#EA580C" />}
              value="0.0"
              label="Rating"
              backgroundColor="#FFF7ED"
              textColor="#EA580C"
            />
            <StatCard
              icon={<AppIcons.person size={24} color="#257587" />}
              value="0"
              label="Reviews"
              backgroundColor="#F0F9FF"
              textColor="#257587"
            />
          </View>

          {/* Profile Information Section */}
          <SectionHeader title="Profile Information" />

          <ProfileItem
            icon={<AppIcons.business size={20} color="#257587" />}
            title="Title"
            subtitle="clinique du testeur 2"
            onPress={() => console.log('Title pressed')}
          />

          <ProfileItem
            icon={<AppIcons.call size={20} color="#257587" />}
            title="Phone"
            subtitle="0558880153"
            onPress={() => console.log('Phone pressed')}
          />

          <ProfileItem
            icon={<AppIcons.people size={20} color="#257587" />}
            title="Category"
            subtitle="Doctor"
            onPress={() => console.log('Category pressed')}
          />

          <ProfileItem
            icon={<AppIcons.checkmarkCircle size={20} color="#27AE60" />}
            title="Setup Status"
            subtitle="Complete"
            onPress={() => console.log('Setup Status pressed')}
          />

          {/* Business Logo Section */}
          <SectionHeader title="Business Logo" />

          <View
            backgroundColor={isDark ? "#1C2127" : "#FFFFFF"}
            borderRadius="$md"
            padding="$lg"
            alignItems="center"
            gap="$lg"
          >
            <View
              backgroundColor={isDark ? "#2A3038" : "#F5F5F5"}
              borderRadius="$md"
              padding="$5xl"
              alignItems="center"
              justifyContent="center"
              width="100%"
              minHeight={120}
            >
              <AppIcons.business size={48} color={isDark ? "#B8BCC8" : "#7F8C8D"} />
              <Text
                fontSize="$bodyMedium"
                color={isDark ? "#B8BCC8" : "#7F8C8D"}
                marginTop="$md"
              >
                No logo uploaded
              </Text>
            </View>

            <Pressable
              style={{
                borderWidth: 1,
                borderColor: '#257587',
                borderRadius: 8,
                paddingVertical: 12,
                paddingHorizontal: 24,
                width: '100%',
                alignItems: 'center',
              }}
              onPress={() => console.log('Change logo pressed')}
            >
              <View flexDirection="row" alignItems="center" gap="$sm">
                <AppIcons.edit size={16} color="#257587" />
                <Text
                  fontSize="$bodyMedium"
                  color="#257587"
                  fontWeight="500"
                >
                  Change Logo
                </Text>
              </View>
            </Pressable>

            <Text
              fontSize="$bodySmall"
              color={isDark ? "#B8BCC8" : "#7F8C8D"}
              textAlign="center"
            >
              Supported formats: PNG, JPG, JPEG • Max size: 5MB
            </Text>
          </View>

          {/* Bottom spacing */}
          <View height={40} />
        </View>
      </ScrollView>
    </View>
  )
}
