{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,13073", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,13154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1455,1545,1609,1677,1739,1812,1876,1947,2023,2079,2133,2259,2317,2379,2433,2509,2652,2739,2819,2918,3004,3086,3225,3307,3389,3531,3618,3698,3754,3805,3871,3946,4026,4097,4176,4249,4326,4395,4469,4576,4669,4746,4839,4931,5005,5086,5179,5232,5316,5382,5465,5553,5615,5679,5742,5810,5926,6034,6141,6243,6341,6433,6493,6548,6634,6717,6796", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,79,89,63,67,61,72,63,70,75,55,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,141,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,91,73,80,92,52,83,65,82,87,61,63,62,67,115,107,106,101,97,91,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1450,1540,1604,1672,1734,1807,1871,1942,2018,2074,2128,2254,2312,2374,2428,2504,2647,2734,2814,2913,2999,3081,3220,3302,3384,3526,3613,3693,3749,3800,3866,3941,4021,4092,4171,4244,4321,4390,4464,4571,4664,4741,4834,4926,5000,5081,5174,5227,5311,5377,5460,5548,5610,5674,5737,5805,5921,6029,6136,6238,6336,6428,6488,6543,6629,6712,6791,6870"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3123,3204,3284,3366,3465,4293,4396,4516,5010,5070,5134,6954,7186,7321,7401,7491,7555,7623,7685,7758,7822,7893,7969,8025,8079,8205,8263,8325,8379,8455,8756,8843,8923,9022,9108,9190,9329,9411,9493,9635,9722,9802,9858,9909,9975,10050,10130,10201,10280,10353,10430,10499,10573,10680,10773,10850,10943,11035,11109,11190,11283,11336,11420,11486,11569,11657,11719,11783,11846,11914,12030,12138,12245,12347,12445,12537,12597,12652,13159,13242,13321", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,79,89,63,67,61,72,63,70,75,55,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,141,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,91,73,80,92,52,83,65,82,87,61,63,62,67,115,107,106,101,97,91,59,54,85,82,78,78", "endOffsets": "318,3199,3279,3361,3460,3556,4391,4511,4592,5065,5129,5221,7028,7246,7396,7486,7550,7618,7680,7753,7817,7888,7964,8020,8074,8200,8258,8320,8374,8450,8593,8838,8918,9017,9103,9185,9324,9406,9488,9630,9717,9797,9853,9904,9970,10045,10125,10196,10275,10348,10425,10494,10568,10675,10768,10845,10938,11030,11104,11185,11278,11331,11415,11481,11564,11652,11714,11778,11841,11909,12025,12133,12240,12342,12440,12532,12592,12647,12733,13237,13316,13395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4799,5226,5325,5437", "endColumns": "115,98,111,102", "endOffsets": "4910,5320,5432,5535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,351,434,504,583,662,750,834,908,997,1081,1157,1238,1320,1395,1473,1547,1637,1709,1795,1871", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "119,205,276,346,429,499,578,657,745,829,903,992,1076,1152,1233,1315,1390,1468,1542,1632,1704,1790,1866,1952"}, "to": {"startLines": "33,49,69,71,72,74,92,93,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,4597,6883,7033,7103,7251,8598,8677,12738,12826,12910,12984,13400,13484,13560,13641,13723,13798,13876,13950,14141,14213,14299,14375", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "3118,4678,6949,7098,7181,7316,8672,8751,12821,12905,12979,13068,13479,13555,13636,13718,13793,13871,13945,14035,14208,14294,14370,14456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3561,3658,3760,3859,3959,4066,4172,14040", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3653,3755,3854,3954,4061,4167,4288,14136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,266,395,534,681,816,945,1092,1194,1334,1483", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "166,261,390,529,676,811,940,1087,1189,1329,1478,1604"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4915,5540,5669,5808,5955,6090,6219,6366,6468,6608,6757", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "4794,5005,5664,5803,5950,6085,6214,6361,6463,6603,6752,6878"}}]}]}