// API Configuration Constants

export const API_CONFIG = {
  BASE_URL: 'https://dapi-test.adscloud.org:8443',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/provider/login',
    REQUEST_EMAIL_OTP: '/api/auth/request-email-otp',
    VERIFY_OTP_REGISTER: '/api/auth/provider/verify-otp-register',
    VERIFY_OTP_RESET_PASSWORD: '/api/auth/verify-otp-reset-password',
    REFRESH_TOKEN: '/api/auth/refresh-token',
    LOGOUT: '/api/auth/logout',
    // Password Reset Workflow APIs
    REQUEST_PASSWORD_RESET_OTP: '/api/auth/request-password-reset-otp',
    VERIFY_PASSWORD_RESET_OTP: '/api/auth/verify-password-reset-otp',
    RESET_PASSWORD: '/api/auth/reset-password',
    PROVIDER_CATEGORIES: '/api/auth/provider/categories',
  },

  // Provider Profile
  PROVIDER: {
    PROFILE: '/provider/profile',
    UPDATE_PROFILE: '/provider/profile',
    SETUP_COMPLETE: '/provider/setup-complete',
    DASHBOARD: '/provider/dashboard',
    ACTIVE_SESSIONS: '/provider/dashboard/active-sessions',
    PENDING_APPOINTMENTS: '/provider/dashboard/pending-appointments',
    TODAY_APPOINTMENTS: '/provider/appointments/today',
    PROFILE_COMPLETION: '/api/auth/provider/profile-completion',
  },

  // Locations
  LOCATIONS: {
    LIST: '/provider/locations',
    CREATE: '/provider/locations',
    GET: (id: number) => `/provider/locations/${id}`,
    UPDATE: (id: number) => `/provider/locations/${id}`,
    DELETE: (id: number) => `/provider/locations/${id}`,
  },

  // Services
  SERVICES: {
    LIST: '/provider/services',
    CREATE: '/provider/services',
    GET: (id: number) => `/provider/services/${id}`,
    UPDATE: (id: number) => `/provider/services/${id}`,
    DELETE: (id: number) => `/provider/services/${id}`,
    CATEGORIES: '/provider/services/categories',
  },

  // Queues
  QUEUES: {
    LIST: '/provider/queues',
    CREATE: '/provider/queues',
    GET: (id: number) => `/provider/queues/${id}`,
    UPDATE: (id: number) => `/provider/queues/${id}`,
    DELETE: (id: number) => `/provider/queues/${id}`,
    STATUS: (id: number) => `/provider/queues/${id}/status`,
    CUSTOMERS: (id: number) => `/provider/queues/${id}/customers`,
  },

  // Appointments
  APPOINTMENTS: {
    LIST: '/provider/appointments',
    CREATE: '/provider/appointments',
    GET: (id: number) => `/provider/appointments/${id}`,
    UPDATE: (id: number) => `/provider/appointments/${id}`,
    DELETE: (id: number) => `/provider/appointments/${id}`,
    RESCHEDULE: (id: number) => `/provider/appointments/${id}/reschedule`,
    COMPLETE: (id: number) => `/provider/appointments/${id}/complete`,
    CANCEL: (id: number) => `/provider/appointments/${id}/cancel`,
    TODAY: '/provider/appointments/today',
    UPCOMING: '/provider/appointments/upcoming',
  },

  // Customers
  CUSTOMERS: {
    LIST: '/provider/customers',
    CREATE: '/provider/customers',
    GET: (id: string) => `/provider/customers/${id}`,
    UPDATE: (id: string) => `/provider/customers/${id}`,
    DELETE: (id: string) => `/provider/customers/${id}`,
    SEARCH: '/provider/customers/search',
    HISTORY: (id: string) => `/provider/customers/${id}/history`,
  },

  // Messaging
  MESSAGES: {
    CONVERSATIONS: '/provider/messages/conversations',
    CONVERSATION: (id: number) => `/provider/messages/conversations/${id}`,
    SEND: (conversationId: number) => `/provider/messages/conversations/${conversationId}/send`,
    MARK_READ: (conversationId: number) => `/provider/messages/conversations/${conversationId}/mark-read`,
  },

  // Notifications
  NOTIFICATIONS: {
    LIST: '/provider/notifications',
    MOBILE_LIST: '/api/auth/notifications/mobile/list',
    MARK_READ: (id: number) => `/provider/notifications/${id}/mark-read`,
    MARK_READ_MOBILE: '/api/auth/notifications/mobile/mark-as-read',
    DASHBOARD_MARK_READ: (id: string) => `/api/provider/dashboard/notifications/${id}/read`,
    MARK_ALL_READ: '/provider/notifications/mark-all-read',
    SETTINGS: '/provider/notifications/settings',
  },

  // Analytics
  ANALYTICS: {
    REVENUE: '/provider/analytics/revenue',
    APPOINTMENTS: '/provider/analytics/appointments',
    CUSTOMERS: '/provider/analytics/customers',
    PERFORMANCE: '/provider/analytics/performance',
  },
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNAUTHORIZED: 'Your session has expired. Please log in again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Something went wrong on our end. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  PROVIDER_DATA: 'provider_data',
  THEME_PREFERENCE: 'theme_preference',
  LANGUAGE_PREFERENCE: 'language_preference',
  NOTIFICATION_SETTINGS: 'notification_settings',
  ONBOARDING_COMPLETED: 'onboarding_completed',
} as const;

export const QUERY_KEYS = {
  // Authentication
  AUTH_USER: ['auth', 'user'],
  PROVIDER_PROFILE: ['provider', 'profile'],
  PROVIDER_CATEGORIES: ['provider', 'categories'],

  // Dashboard
  DASHBOARD_OVERVIEW: ['dashboard', 'overview'],
  TODAY_SCHEDULE: ['dashboard', 'today-schedule'],
  QUICK_STATS: ['dashboard', 'quick-stats'],

  // Locations
  LOCATIONS: ['locations'],
  LOCATION: (id: number) => ['locations', id],

  // Services
  SERVICES: ['services'],
  SERVICE: (id: number) => ['services', id],
  SERVICE_CATEGORIES: ['services', 'categories'],

  // Queues
  QUEUES: ['queues'],
  QUEUE: (id: number) => ['queues', id],
  QUEUE_STATUS: (id: number) => ['queues', id, 'status'],
  QUEUE_CUSTOMERS: (id: number) => ['queues', id, 'customers'],

  // Appointments
  APPOINTMENTS: ['appointments'],
  APPOINTMENT: (id: number) => ['appointments', id],
  TODAY_APPOINTMENTS: ['appointments', 'today'],
  UPCOMING_APPOINTMENTS: ['appointments', 'upcoming'],

  // Customers
  CUSTOMERS: ['customers'],
  CUSTOMER: (id: string) => ['customers', id],
  CUSTOMER_HISTORY: (id: string) => ['customers', id, 'history'],

  // Messages
  CONVERSATIONS: ['messages', 'conversations'],
  CONVERSATION: (id: number) => ['messages', 'conversations', id],

  // Notifications
  NOTIFICATIONS: ['notifications'],
  NOTIFICATION_SETTINGS: ['notifications', 'settings'],

  // Analytics
  REVENUE_ANALYTICS: ['analytics', 'revenue'],
  APPOINTMENT_ANALYTICS: ['analytics', 'appointments'],
  CUSTOMER_ANALYTICS: ['analytics', 'customers'],
  PERFORMANCE_ANALYTICS: ['analytics', 'performance'],
} as const;

export const WEBSOCKET_EVENTS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',

  // Appointments
  APPOINTMENT_CREATED: 'appointment:created',
  APPOINTMENT_UPDATED: 'appointment:updated',
  APPOINTMENT_CANCELLED: 'appointment:cancelled',
  APPOINTMENT_COMPLETED: 'appointment:completed',

  // Queue Updates
  QUEUE_UPDATED: 'queue:updated',
  CUSTOMER_JOINED_QUEUE: 'queue:customer_joined',
  CUSTOMER_LEFT_QUEUE: 'queue:customer_left',

  // Messages
  NEW_MESSAGE: 'message:new',
  MESSAGE_READ: 'message:read',

  // Notifications
  NEW_NOTIFICATION: 'notification:new',
} as const;
