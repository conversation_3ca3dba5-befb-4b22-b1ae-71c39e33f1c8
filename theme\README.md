# Dalti Custom Theme System

A comprehensive theme system built on top of Tamagui, implementing the Dalti brand design tokens and providing a powerful, type-safe theming solution for React Native applications.

## 🎨 Features

- **Complete Design Token System**: All Dalti brand colors, typography, spacing, and layout tokens
- **Light & Dark Theme Support**: Automatic system theme detection with manual override
- **Type-Safe**: Full TypeScript support with autocomplete for all design tokens
- **Tamagui Integration**: Built on Tamagui's powerful styling system
- **Theme Context**: React context for theme state management and utilities
- **Responsive Design**: Breakpoint system for responsive layouts
- **Animation Tokens**: Predefined durations and easing functions
- **Utility Functions**: Helper functions for common theme operations

## 📁 File Structure

```
theme/
├── index.ts                 # Main exports
├── tokens.ts               # Design tokens (colors, spacing, typography)
├── themes.ts               # Light and dark theme definitions
├── utils.ts                # Utility functions and constants
├── DaltiThemeProvider.tsx  # Custom theme provider component
└── README.md               # This documentation
```

## 🚀 Quick Start

### 1. Basic Usage

```tsx
import React from 'react'
import { View, Text } from '@tamagui/core'
import { DaltiThemeProvider, useTheme } from './theme'

function App() {
  return (
    <DaltiThemeProvider defaultTheme="system">
      <MyComponent />
    </DaltiThemeProvider>
  )
}

function MyComponent() {
  const { theme, toggleTheme } = useTheme()
  
  return (
    <View padding="$lg" backgroundColor="$surface">
      <Text color="$color" fontSize="$headlineLarge">
        Current theme: {theme}
      </Text>
    </View>
  )
}
```

### 2. Using Design Tokens

```tsx
import { View, Text } from '@tamagui/core'

function BrandCard() {
  return (
    <View 
      backgroundColor="$primary"
      padding="$lg"
      borderRadius="$card"
      gap="$md"
    >
      <Text 
        color="$colorOnPrimary" 
        fontSize="$titleLarge"
        fontWeight="semibold"
      >
        Dalti Brand Card
      </Text>
      <Text color="$colorOnPrimary" fontSize="$bodyMedium">
        Using custom design tokens
      </Text>
    </View>
  )
}
```

## 🎯 Design Tokens

### Colors

#### Brand Colors
- `$primary` - #15424E (Stormy Sea)
- `$primaryVariant` - #0D3339
- `$secondary` - #4ECDC4 (Teal Breeze)
- `$accent` - #FFE66D (Golden Yellow)

#### Status Colors
- `$success` - #27AE60
- `$error` - #E74C3C
- `$warning` - #F39C12
- `$statusActive` - #27AE60
- `$statusInactive` - #F39C12
- `$statusBlocked` - #E74C3C
- `$statusPending` - #3498DB

#### Theme-Aware Colors
- `$background` - Background color (theme-aware)
- `$surface` - Surface color (theme-aware)
- `$color` - Primary text color (theme-aware)
- `$colorSecondary` - Secondary text color (theme-aware)
- `$colorOnPrimary` - Text on primary backgrounds
- `$borderColor` - Border color (theme-aware)

### Spacing & Sizing

```tsx
// Spacing tokens (4px base unit)
$xs: 4px    // 1 unit
$sm: 8px    // 2 units  
$md: 12px   // 3 units
$lg: 16px   // 4 units - Primary spacing
$xl: 20px   // 5 units
$2xl: 24px  // 6 units
$3xl: 32px  // 8 units
$4xl: 40px  // 10 units
$5xl: 48px  // 12 units
$6xl: 64px  // 16 units
```

### Typography

```tsx
// Font sizes
$displayLarge: 32px
$displayMedium: 28px
$displaySmall: 24px
$headlineLarge: 22px
$headlineMedium: 20px
$headlineSmall: 18px
$titleLarge: 16px
$titleMedium: 14px
$titleSmall: 12px
$bodyLarge: 16px
$bodyMedium: 14px
$bodySmall: 12px
$labelLarge: 14px
$labelMedium: 12px
$labelSmall: 10px
```

### Border Radius

```tsx
$xs: 2px
$sm: 4px
$md: 8px     // Primary border radius
$lg: 12px
$xl: 16px
$2xl: 20px
$full: 9999px

// Component-specific
$button: 8px
$card: 8px
$input: 8px
$chip: 20px
$fab: 16px
```

## 🔧 Theme Provider Props

```tsx
interface DaltiThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: 'light' | 'dark' | 'system'  // Default: 'system'
  forcedTheme?: 'light' | 'dark'              // Force specific theme
}
```

## 🎣 Hooks

### useTheme()
```tsx
const { 
  theme,        // Current theme: 'light' | 'dark'
  toggleTheme,  // Function to toggle theme
  setTheme,     // Function to set specific theme
  isDark,       // Boolean: is dark theme active
  isLight       // Boolean: is light theme active
} = useTheme()
```

### Utility Hooks
```tsx
const isDark = useIsDark()           // Boolean
const isLight = useIsLight()         // Boolean  
const toggleTheme = useThemeToggle() // Function
```

## 🛠 Utility Functions

```tsx
import { getSpacing, getSize, getRadius, getColor } from './theme'

// Get token values
const spacing = getSpacing('lg')     // Returns 16
const size = getSize('iconLg')       // Returns 24
const radius = getRadius('card')     // Returns 8
const color = getColor('primary')    // Returns '#15424E'
```

## 📱 Responsive Design

```tsx
import { breakpoints, mediaQueries } from './theme'

// Breakpoints
breakpoints.xs   // 0
breakpoints.sm   // 480
breakpoints.md   // 768
breakpoints.lg   // 1024
breakpoints.xl   // 1280
breakpoints['2xl'] // 1536

// Media queries (for web)
mediaQueries.sm  // '@media (min-width: 480px)'
```

## 🎭 Animation Tokens

```tsx
import { animations } from './theme'

// Durations
animations.duration.fast     // 150ms
animations.duration.normal   // 300ms
animations.duration.slow     // 500ms

// Easing functions
animations.easing.material   // 'cubic-bezier(0.4, 0.0, 0.2, 1)'
```

## 🌟 Advanced Usage

### Theme-Aware Styles
```tsx
import { createThemeAwareStyle } from './theme'

const styles = createThemeAwareStyle(
  { backgroundColor: '#FFFFFF' }, // Light theme
  { backgroundColor: '#000000' }  // Dark theme
)
```

### HOC for Theme-Aware Components
```tsx
import { withTheme } from './theme'

const ThemedComponent = withTheme(MyComponent)
```

## 🎨 Customization

To customize the theme, modify the files in the `theme/` directory:

1. **tokens.ts** - Update design tokens
2. **themes.ts** - Modify light/dark theme colors
3. **utils.ts** - Add utility functions
4. **DaltiThemeProvider.tsx** - Extend provider functionality

## 📝 Best Practices

1. **Use Design Tokens**: Always use tokens instead of hardcoded values
2. **Theme-Aware Colors**: Use theme-aware colors for proper dark mode support
3. **Consistent Spacing**: Use the 4px grid system for spacing
4. **Typography Scale**: Use predefined typography tokens
5. **Component Variants**: Create reusable component variants with tokens

## 🔍 Example Components

Check `components/ui/TamaguiTest.tsx` for a comprehensive example showcasing:
- Theme switching
- Brand colors
- Status colors  
- Typography scale
- Proper token usage

This theme system provides a solid foundation for building consistent, accessible, and beautiful React Native applications with the Dalti brand identity.
