{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1545,1637,1704,1778,1839,1918,1982,2052,2126,2183,2237,2353,2412,2474,2528,2610,2739,2831,2906,3001,3082,3166,3310,3389,3470,3617,3710,3789,3844,3895,3961,4040,4121,4192,4272,4344,4422,4497,4569,4680,4777,4854,4952,5044,5122,5203,5295,5352,5436,5502,5585,5672,5734,5798,5861,5937,6039,6146,6243,6349,6439,6529,6588,6643,6732,6819,6896", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,89,91,66,73,60,78,63,69,73,56,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,91,77,80,91,56,83,65,82,86,61,63,62,75,101,106,96,105,89,89,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1540,1632,1699,1773,1834,1913,1977,2047,2121,2178,2232,2348,2407,2469,2523,2605,2734,2826,2901,2996,3077,3161,3305,3384,3465,3612,3705,3784,3839,3890,3956,4035,4116,4187,4267,4339,4417,4492,4564,4675,4772,4849,4947,5039,5117,5198,5290,5347,5431,5497,5580,5667,5729,5793,5856,5932,6034,6141,6238,6344,6434,6524,6583,6638,6727,6814,6891,6972"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3153,3260,3368,3450,3551,4371,4471,4593,5075,5140,5206,7122,7285,7414,7504,7596,7663,7737,7798,7877,7941,8011,8085,8142,8196,8312,8371,8433,8487,8569,8858,8950,9025,9120,9201,9285,9429,9508,9589,9736,9829,9908,9963,10014,10080,10159,10240,10311,10391,10463,10541,10616,10688,10799,10896,10973,11071,11163,11241,11322,11414,11471,11555,11621,11704,11791,11853,11917,11980,12056,12158,12265,12362,12468,12558,12648,12707,12762,13279,13366,13443", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,89,91,66,73,60,78,63,69,73,56,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,91,77,80,91,56,83,65,82,86,61,63,62,75,101,106,96,105,89,89,58,54,88,86,76,80", "endOffsets": "328,3255,3363,3445,3546,3643,4466,4588,4673,5135,5201,5298,7197,7342,7499,7591,7658,7732,7793,7872,7936,8006,8080,8137,8191,8307,8366,8428,8482,8564,8693,8945,9020,9115,9196,9280,9424,9503,9584,9731,9824,9903,9958,10009,10075,10154,10235,10306,10386,10458,10536,10611,10683,10794,10891,10968,11066,11158,11236,11317,11409,11466,11550,11616,11699,11786,11848,11912,11975,12051,12153,12260,12357,12463,12553,12643,12702,12757,12846,13361,13438,13519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4876,5303,5405,5524", "endColumns": "106,101,118,104", "endOffsets": "4978,5400,5519,5624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,13192", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,13274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "33,49,69,71,73,91,92,141,142,143,144,149,150,151,152,153,154,155,156,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3083,4678,7052,7202,7347,8698,8777,12851,12941,13033,13104,13524,13619,13710,13790,13870,13953,14030,14103,14292,14364,14447,14520", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3148,4753,7117,7280,7409,8772,8853,12936,13028,13099,13187,13614,13705,13785,13865,13948,14025,14098,14186,14359,14442,14515,14595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3648,3746,3848,3947,4049,4153,4257,14191", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3741,3843,3942,4044,4148,4252,4366,14287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,265,386,549,703,837,968,1148,1251,1385,1548", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "168,260,381,544,698,832,963,1143,1246,1380,1543,1683"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4983,5629,5750,5913,6067,6201,6332,6512,6615,6749,6912", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "4871,5070,5745,5908,6062,6196,6327,6507,6610,6744,6907,7047"}}]}]}