{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,4530,6720,6854,6925,7066,8338,8405,8479,12502,12582,12662,12730,13128,13210,13285,13371,13458,13533,13604,13675,13867,13939,14014,14083", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3063,4604,6784,6920,7002,7128,8400,8474,8550,12577,12657,12725,12808,13205,13280,13366,13453,13528,13599,13670,13761,13934,14009,14078,14151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3500,3600,3704,3805,3908,4010,4115,13766", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3595,3699,3800,3903,4005,4110,4227,13862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,249,371,500,629,763,895,1029,1125,1269,1414", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "157,244,366,495,624,758,890,1024,1120,1264,1409,1535"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4609,4817,5429,5551,5680,5809,5943,6075,6209,6305,6449,6594", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "4711,4899,5546,5675,5804,5938,6070,6204,6300,6444,6589,6715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,12813", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,12888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4716,5115,5216,5327", "endColumns": "100,100,110,101", "endOffsets": "4812,5211,5322,5424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1405,1493,1555,1617,1677,1744,1807,1873,1944,2000,2054,2168,2225,2286,2340,2410,2529,2610,2687,2776,2858,2943,3078,3155,3232,3373,3459,3543,3599,3651,3717,3787,3865,3936,4018,4088,4164,4235,4304,4418,4514,4588,4686,4782,4856,4926,5028,5083,5171,5238,5325,5418,5481,5545,5608,5674,5774,5883,5977,6084,6182,6282,6342,6398,6476,6560,6638", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,80,87,61,61,59,66,62,65,70,55,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,97,99,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1400,1488,1550,1612,1672,1739,1802,1868,1939,1995,2049,2163,2220,2281,2335,2405,2524,2605,2682,2771,2853,2938,3073,3150,3227,3368,3454,3538,3594,3646,3712,3782,3860,3931,4013,4083,4159,4230,4299,4413,4509,4583,4681,4777,4851,4921,5023,5078,5166,5233,5320,5413,5476,5540,5603,5669,5769,5878,5972,6079,6177,6277,6337,6393,6471,6555,6633,6706"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3068,3153,3240,3323,3416,4232,4332,4448,4904,4961,5024,6789,7007,7133,7214,7302,7364,7426,7486,7553,7616,7682,7753,7809,7863,7977,8034,8095,8149,8219,8555,8636,8713,8802,8884,8969,9104,9181,9258,9399,9485,9569,9625,9677,9743,9813,9891,9962,10044,10114,10190,10261,10330,10444,10540,10614,10712,10808,10882,10952,11054,11109,11197,11264,11351,11444,11507,11571,11634,11700,11800,11909,12003,12110,12208,12308,12368,12424,12893,12977,13055", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,80,87,61,61,59,66,62,65,70,55,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,97,99,59,55,77,83,77,72", "endOffsets": "304,3148,3235,3318,3411,3495,4327,4443,4525,4956,5019,5110,6849,7061,7209,7297,7359,7421,7481,7548,7611,7677,7748,7804,7858,7972,8029,8090,8144,8214,8333,8631,8708,8797,8879,8964,9099,9176,9253,9394,9480,9564,9620,9672,9738,9808,9886,9957,10039,10109,10185,10256,10325,10439,10535,10609,10707,10803,10877,10947,11049,11104,11192,11259,11346,11439,11502,11566,11629,11695,11795,11904,11998,12105,12203,12303,12363,12419,12497,12972,13050,13123"}}]}]}