import React, { forwardRef, useState } from 'react'
import { TextInput, Pressable } from 'react-native'
import { View, Text } from '@tamagui/core'
import { useTheme } from '../../theme/DaltiThemeProvider'
import { Input, InputProps } from './Input'

// Simple icon components (you can replace with your preferred icon library)
const SearchIcon = ({ size = 20, color = '#A0A0A0' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 2}
    backgroundColor={color}
    opacity={0.3}
  />
)

const ClearIcon = ({ size = 16, color = '#A0A0A0' }) => (
  <View
    width={size}
    height={size}
    borderRadius={size / 2}
    backgroundColor={color}
    opacity={0.5}
  />
)

export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'rightIcon' | 'onRightIconPress'> {
  onSearch?: (query: string) => void
  onClear?: () => void
  showClearButton?: boolean
  searchIconSize?: number
  clearIconSize?: number
}

export const SearchInput = forwardRef<TextInput, SearchInputProps>(({
  onSearch,
  onClear,
  showClearButton = true,
  searchIconSize = 20,
  clearIconSize = 16,
  placeholder = 'Search...',
  value,
  onChangeText,
  ...inputProps
}, ref) => {
  const { isDark } = useTheme()
  const [searchQuery, setSearchQuery] = useState(value || '')

  const handleChangeText = (text: string) => {
    setSearchQuery(text)
    onChangeText?.(text)
    onSearch?.(text)
  }

  const handleClear = () => {
    setSearchQuery('')
    onChangeText?.('')
    onClear?.()
  }

  const iconColor = isDark ? '#B8BCC8' : '#A0A0A0'

  return (
    <Input
      ref={ref}
      placeholder={placeholder}
      value={searchQuery}
      onChangeText={handleChangeText}
      leftIcon={<SearchIcon size={searchIconSize} color={iconColor} />}
      rightIcon={
        showClearButton && searchQuery.length > 0 ? (
          <ClearIcon size={clearIconSize} color={iconColor} />
        ) : undefined
      }
      onRightIconPress={showClearButton && searchQuery.length > 0 ? handleClear : undefined}
      {...inputProps}
    />
  )
})

SearchInput.displayName = 'SearchInput'
