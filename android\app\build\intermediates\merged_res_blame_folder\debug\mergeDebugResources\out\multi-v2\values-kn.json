{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,277,361,444,526,641,736,843,956,1041,1098,1161,1255,1321,1383,1467,1570,1636,1707,1766,1842,1907,1975,2047,2103,2157,2270,2328,2389,2443,2522,2638,2724,2807,2902,2988,3079,3221,3300,3379,3510,3598,3682,3739,3791,3857,3937,4027,4098,4177,4254,4331,4408,4477,4594,4693,4770,4863,4958,5032,5113,5209,5264,5348,5416,5502,5590,5653,5718,5781,5849,5954,6059,6154,6257,6355,6451,6512,6568,6650,6742,6821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,83,102,65,70,58,75,64,67,71,55,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,130,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,54,83,67,85,87,62,64,62,67,104,104,94,102,97,95,60,55,81,91,78,73", "endOffsets": "272,356,439,521,636,731,838,951,1036,1093,1156,1250,1316,1378,1462,1565,1631,1702,1761,1837,1902,1970,2042,2098,2152,2265,2323,2384,2438,2517,2633,2719,2802,2897,2983,3074,3216,3295,3374,3505,3593,3677,3734,3786,3852,3932,4022,4093,4172,4249,4326,4403,4472,4589,4688,4765,4858,4953,5027,5108,5204,5259,5343,5411,5497,5585,5648,5713,5776,5844,5949,6054,6149,6252,6350,6446,6507,6563,6645,6737,6816,6890"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3150,3234,3317,3399,3514,4354,4461,4574,5049,5106,5169,7000,7216,7346,7430,7533,7599,7670,7729,7805,7870,7938,8010,8066,8120,8233,8291,8352,8406,8485,8818,8904,8987,9082,9168,9259,9401,9480,9559,9690,9778,9862,9919,9971,10037,10117,10207,10278,10357,10434,10511,10588,10657,10774,10873,10950,11043,11138,11212,11293,11389,11444,11528,11596,11682,11770,11833,11898,11961,12029,12134,12239,12334,12437,12535,12631,12692,12748,13231,13323,13402", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,83,102,65,70,58,75,64,67,71,55,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,130,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,54,83,67,85,87,62,64,62,67,104,104,94,102,97,95,60,55,81,91,78,73", "endOffsets": "322,3229,3312,3394,3509,3604,4456,4569,4654,5101,5164,5258,7061,7273,7425,7528,7594,7665,7724,7800,7865,7933,8005,8061,8115,8228,8286,8347,8401,8480,8596,8899,8982,9077,9163,9254,9396,9475,9554,9685,9773,9857,9914,9966,10032,10112,10202,10273,10352,10429,10506,10583,10652,10769,10868,10945,11038,11133,11207,11288,11384,11439,11523,11591,11677,11765,11828,11893,11956,12024,12129,12234,12329,12432,12530,12626,12687,12743,12825,13318,13397,13471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,252,383,515,654,788,925,1074,1174,1324,1472", "endColumns": "108,87,130,131,138,133,136,148,99,149,147,127", "endOffsets": "159,247,378,510,649,783,920,1069,1169,1319,1467,1595"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4738,4961,5580,5711,5843,5982,6116,6253,6402,6502,6652,6800", "endColumns": "108,87,130,131,138,133,136,148,99,149,147,127", "endOffsets": "4842,5044,5706,5838,5977,6111,6248,6397,6497,6647,6795,6923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4847,5263,5363,5479", "endColumns": "113,99,115,100", "endOffsets": "4956,5358,5474,5575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "327,441,553,666,751,858,984,1062,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2075,2157,2273,2383,2482,2595,2700,2814,2978,13148", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "436,548,661,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,3073,13226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,347,428,496,563,638,713,798,879,950,1031,1111,1189,1271,1358,1435,1506,1576,1671,1743,1821,1890", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "122,201,273,342,423,491,558,633,708,793,874,945,1026,1106,1184,1266,1353,1430,1501,1571,1666,1738,1816,1885,1960"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3078,4659,6928,7066,7135,7278,8601,8668,8743,12830,12915,12996,13067,13476,13556,13634,13716,13803,13880,13951,14021,14217,14289,14367,14436", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "3145,4733,6995,7130,7211,7341,8663,8738,8813,12910,12991,13062,13143,13551,13629,13711,13798,13875,13946,14016,14111,14284,14362,14431,14506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3609,3707,3810,3911,4017,4118,4226,14116", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3702,3805,3906,4012,4113,4221,4349,14212"}}]}]}