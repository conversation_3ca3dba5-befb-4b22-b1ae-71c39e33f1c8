import React, { useState } from 'react'
import { <PERSON>rollView } from 'react-native'
import { View, Text } from '@tamagui/core'
import { Input } from './Input'
import { TextArea } from './TextArea'
import { SearchInput } from './SearchInput'
import { PasswordInput } from './PasswordInput'

export function InputShowcase() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    search: '',
    password: '',
    confirmPassword: '',
    bio: '',
    feedback: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleEmailBlur = () => {
    if (formData.email && !validateEmail(formData.email)) {
      setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }))
    }
  }

  const handlePasswordConfirmBlur = () => {
    if (formData.confirmPassword && formData.password !== formData.confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match' }))
    }
  }

  return (
    <ScrollView style={{ flex: 1 }}>
      <View padding="$lg" gap="$xl" backgroundColor="$background">
        <Text fontSize="$headlineLarge" fontWeight="bold" color="$color" textAlign="center">
          Input Components Showcase
        </Text>

        {/* Basic Inputs */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Basic Inputs
          </Text>

          <Input
            label="Full Name"
            placeholder="Enter your full name"
            value={formData.name}
            onChangeText={handleInputChange('name')}
            required
          />

          <Input
            label="Email Address"
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={handleInputChange('email')}
            onBlur={handleEmailBlur}
            error={errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            required
          />

          <Input
            label="Phone Number"
            placeholder="Enter your phone number"
            value={formData.phone}
            onChangeText={handleInputChange('phone')}
            keyboardType="phone-pad"
            helperText="Include country code if international"
          />
        </View>

        {/* Input Sizes */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Input Sizes
          </Text>

          <Input
            label="Small Input"
            placeholder="Small size input"
            size="sm"
          />

          <Input
            label="Medium Input (Default)"
            placeholder="Medium size input"
            size="md"
          />

          <Input
            label="Large Input"
            placeholder="Large size input"
            size="lg"
          />
        </View>

        {/* Input Variants */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Input Variants
          </Text>

          <Input
            label="Outlined Input (Default)"
            placeholder="Outlined variant"
            variant="outlined"
          />

          <Input
            label="Filled Input"
            placeholder="Filled variant"
            variant="filled"
          />
        </View>

        {/* Search Input */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Search Input
          </Text>

          <SearchInput
            label="Search"
            placeholder="Search for anything..."
            value={formData.search}
            onChangeText={handleInputChange('search')}
            onSearch={(query) => console.log('Searching for:', query)}
            helperText="Search results will appear as you type"
          />
        </View>

        {/* Password Inputs */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Password Inputs
          </Text>

          <PasswordInput
            label="Password"
            placeholder="Enter your password"
            value={formData.password}
            onChangeText={handleInputChange('password')}
            showStrengthIndicator
            required
          />

          <PasswordInput
            label="Confirm Password"
            placeholder="Confirm your password"
            value={formData.confirmPassword}
            onChangeText={handleInputChange('confirmPassword')}
            onBlur={handlePasswordConfirmBlur}
            error={errors.confirmPassword}
            required
          />
        </View>

        {/* Text Areas */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Text Areas
          </Text>

          <TextArea
            label="Bio"
            placeholder="Tell us about yourself..."
            value={formData.bio}
            onChangeText={handleInputChange('bio')}
            rows={4}
            maxLength={500}
            showCharacterCount
            helperText="Share a brief description about yourself"
          />

          <TextArea
            label="Feedback"
            placeholder="Your feedback is valuable to us..."
            value={formData.feedback}
            onChangeText={handleInputChange('feedback')}
            rows={6}
            required
          />
        </View>

        {/* Disabled State */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Disabled State
          </Text>

          <Input
            label="Disabled Input"
            placeholder="This input is disabled"
            value="Cannot edit this"
            disabled
          />

          <TextArea
            label="Disabled TextArea"
            placeholder="This textarea is disabled"
            value="Cannot edit this content"
            disabled
            rows={3}
          />
        </View>

        {/* Error States */}
        <View gap="$lg">
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">
            Error States
          </Text>

          <Input
            label="Input with Error"
            placeholder="This has an error"
            value="invalid@email"
            error="Please enter a valid email address"
          />

          <TextArea
            label="TextArea with Error"
            placeholder="This has an error"
            value="Some invalid content"
            error="Content does not meet requirements"
            rows={3}
          />
        </View>

        <View height="$6xl" /> {/* Bottom spacing */}
      </View>
    </ScrollView>
  )
}
