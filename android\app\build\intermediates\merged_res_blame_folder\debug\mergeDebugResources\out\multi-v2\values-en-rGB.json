{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "48,69,88,89,138,139,140,145,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,6601,7960,8034,11914,11996,12076,12468,12649,12723", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4468,6679,8029,8105,11991,12071,12149,12543,12718,12792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,12154", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,12232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1389,1479,1543,1607,1670,1739,1803,1867,1935,1991,2047,2159,2217,2279,2335,2407,2529,2616,2691,2782,2863,2944,3084,3161,3242,3369,3460,3537,3591,3642,3708,3778,3855,3926,4001,4072,4149,4218,4287,4394,4485,4557,4646,4735,4809,4881,4967,5017,5096,5162,5242,5326,5388,5452,5515,5584,5684,5779,5871,5963,6054,6142,6200,6255,6333,6414,6489", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,76,89,63,63,62,68,63,63,67,55,55,111,57,61,55,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,90,87,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1384,1474,1538,1602,1665,1734,1798,1862,1930,1986,2042,2154,2212,2274,2330,2402,2524,2611,2686,2777,2858,2939,3079,3156,3237,3364,3455,3532,3586,3637,3703,3773,3850,3921,3996,4067,4144,4213,4282,4389,4480,4552,4641,4730,4804,4876,4962,5012,5091,5157,5237,5321,5383,5447,5510,5579,5679,5774,5866,5958,6049,6137,6195,6250,6328,6409,6484,6559"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,4767,4827,4892,6534,6684,6743,6820,6910,6974,7038,7101,7170,7234,7298,7366,7422,7478,7590,7648,7710,7766,7838,8110,8197,8272,8363,8444,8525,8665,8742,8823,8950,9041,9118,9172,9223,9289,9359,9436,9507,9582,9653,9730,9799,9868,9975,10066,10138,10227,10316,10390,10462,10548,10598,10677,10743,10823,10907,10969,11033,11096,11165,11265,11360,11452,11544,11635,11723,11781,11836,12237,12318,12393", "endLines": "5,33,34,35,36,37,45,46,47,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,142,143,144", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,76,89,63,63,62,68,63,63,67,55,55,111,57,61,55,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,90,87,57,54,77,80,74,74", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,4822,4887,4977,6596,6738,6815,6905,6969,7033,7096,7165,7229,7293,7361,7417,7473,7585,7643,7705,7761,7833,7955,8192,8267,8358,8439,8520,8660,8737,8818,8945,9036,9113,9167,9218,9284,9354,9431,9502,9577,9648,9725,9794,9863,9970,10061,10133,10222,10311,10385,10457,10543,10593,10672,10738,10818,10902,10964,11028,11091,11160,11260,11355,11447,11539,11630,11718,11776,11831,11909,12313,12388,12463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "49,51,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4473,4677,5287,5406,5535,5673,5800,5918,6049,6149,6275,6414", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4574,4762,5401,5530,5668,5795,5913,6044,6144,6270,6409,6529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "50,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4579,4982,5079,5188", "endColumns": "97,96,108,98", "endOffsets": "4672,5074,5183,5282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,146", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,12548", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,12644"}}]}]}