// Design Tokens - Dalti Brand System
export const tokens = {
  // Color Palette
  color: {
    // Primary Colors
    primary: '#15424E',
    primaryVariant: '#0D3339',
    secondary: '#4ECDC4',
    accent: '#FFE66D',
    
    // Neutral Colors - Light Theme
    surface: '#FAFAFA',
    background: '#FFFFFF',
    error: '#E74C3C',
    success: '#27AE60',
    warning: '#F39C12',
    
    // Text Colors - Light Theme
    textPrimary: '#2C3E50',
    textSecondary: '#7F8C8D',
    textOnPrimary: '#FFFFFF',
    
    // Dark Theme Colors
    darkBackground: '#0F1419',
    darkSurface: '#1C2127',
    darkSurfaceVariant: '#242A30',
    darkSurfaceContainer: '#2A3038',
    darkSurfaceContainerHigh: '#323A42',
    
    // Dark Theme Primary Colors
    darkPrimary: '#257587',
    darkPrimaryContainer: '#1A3A42',
    darkSecondary: '#3ABAB3',
    
    // Dark Theme Text Colors
    darkOnSurface: '#F8F9FA',
    darkOnSurfaceVariant: '#B8BCC8',
    darkOnSurfaceSecondary: '#6C7278',
    darkOnPrimary: '#FFFFFF',
    darkOnPrimaryContainer: '#B8F5FF',
    
    // Additional Dark Theme Colors
    darkInactiveElement: '#3A4048',
    darkProgressBackground: '#2A3038',
    
    // Status Colors
    statusActive: '#27AE60',
    statusInactive: '#F39C12',
    statusBlocked: '#E74C3C',
    statusPending: '#3498DB',
  },
  
  // Spacing System (4px base unit)
  space: {
    0: 0,
    xs: 4,    // 1 unit
    sm: 8,    // 2 units
    md: 7,    // Custom spacing for icon containers
    lg: 16,   // 4 units - Primary spacing
    xl: 20,   // 5 units
    '2xl': 24, // 6 units
    '3xl': 32, // 8 units
    '4xl': 40, // 10 units
    '5xl': 48, // 12 units
    '6xl': 64, // 16 units
  },
  
  // Size tokens
  size: {
    0: 0,
    xs: 4,
    sm: 8,
    md: 7,
    lg: 16,
    xl: 20,
    '2xl': 24,
    '3xl': 32,
    '4xl': 40,
    '5xl': 48,
    '6xl': 64,
    
    // Icon sizes
    iconXs: 12,
    iconSm: 16,
    iconMd: 20,
    iconLg: 24,
    iconXl: 32,
    icon2xl: 48,
    
    // Avatar sizes
    avatarXs: 24,
    avatarSm: 32,
    avatarMd: 40,
    avatarLg: 48,
    avatarXl: 64,
    
    // Button heights
    buttonSm: 32,
    buttonMd: 40,
    buttonLg: 48,
    
    // Input heights
    input: 48,
    inputSm: 40,
    inputLg: 56,

    // Input specific sizes
    inputIconSize: 20,
    inputBorderWidth: 1,
    inputFocusBorderWidth: 2,
  },
  
  // Border Radius
  radius: {
    0: 0,
    xs: 2,
    sm: 4,
    md: 8,     // Primary border radius
    lg: 12,
    xl: 16,
    '2xl': 20,
    full: 9999,
    
    // Component-specific
    button: 8,
    card: 8,
    input: 8,
    chip: 20,
    fab: 16,
    iconContainer: 8,
    iconContainerLarge: 12,
  },
  
  // Z-Index
  zIndex: {
    0: 0,
    1: 1,
    2: 2,
    3: 3,
    4: 4,
    5: 5,
    modal: 1000,
    overlay: 1100,
    dropdown: 1200,
    tooltip: 1300,
    toast: 1400,
  },
}

// Font configuration
export const fonts = {
  body: {
    family: 'Changa, Helvetica Neue, Arial, sans-serif',
    size: {
      // Display Styles
      displayLarge: 32,
      displayMedium: 28,
      displaySmall: 24,
      
      // Headline Styles
      headlineLarge: 22,
      headlineMedium: 20,
      headlineSmall: 18,
      
      // Title Styles
      titleLarge: 16,
      titleMedium: 14,
      titleSmall: 12,
      
      // Body Styles
      bodyLarge: 16,
      bodyMedium: 14,
      bodySmall: 12,
      
      // Label Styles
      labelLarge: 14,
      labelMedium: 12,
      labelSmall: 10,
    },
    weight: {
      light: '300',
      regular: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    lineHeight: {
      // Display Line Heights
      displayLarge: 40,
      displayMedium: 36,
      displaySmall: 32,
      
      // Headline Line Heights
      headlineLarge: 28,
      headlineMedium: 26,
      headlineSmall: 24,
      
      // Title Line Heights
      titleLarge: 22,
      titleMedium: 20,
      titleSmall: 18,
      
      // Body Line Heights
      bodyLarge: 24,
      bodyMedium: 20,
      bodySmall: 18,
      
      // Label Line Heights
      labelLarge: 20,
      labelMedium: 18,
      labelSmall: 16,
    },
  },
}
