import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Text } from 'tamagui';

import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  const iconProps = { size, color };

  switch (name) {
    case 'index':
      return <AppIcons.grid {...iconProps} />;
    case 'calendar':
      return <AppIcons.calendar {...iconProps} />;
    case 'clients':
      return <AppIcons.people {...iconProps} />;
    case 'bookings':
      return <AppIcons.bookmarks {...iconProps} />;
    default:
      return <AppIcons.grid {...iconProps} />;
  }
};

export const NativeBottomTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const insets = useSafeAreaInsets();
  const { isDark } = useTheme();

  // Theme colors - Using Dalti brand colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';
  const activeColor = isDark ? '#257587' : '#15424E'; // Dalti primary colors
  const inactiveColor = isDark ? '#9CA3AF' : '#7F8C8D'; // Neutral colors

  const handleQRPress = () => {
    console.log('🔥 NATIVE TAB BAR - QR Scanner pressed');
    // Add your QR scanner logic here
  };

  return (
    <View
      style={[
        styles.container,
        {
          paddingBottom: insets.bottom,
          backgroundColor: tabBarBg, // Match tab bar background
        }
      ]}>
      {/* Native-style bottom tab bar */}
      <View
        style={[
          styles.tabBar,
          {
            backgroundColor: tabBarBg,
            borderTopColor: borderColor,
          },
        ]}>
        {/* Regular tab buttons */}
        <View style={styles.tabContent}>
          {state.routes.map((route, index) => {
            const { options } = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;

            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name, route.params);
              }
            };

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              });
            };

            // Show all tabs - QR button will be positioned separately

            return (
              <TouchableOpacity
                key={route.key}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={styles.tabButton}>
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={24}
                />
                <Text
                  fontSize={12}
                  color={isFocused ? activeColor : inactiveColor}
                  marginTop={4}>
                  {label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Center QR Button - Positioned between Calendar and Clients */}
        <TouchableOpacity
          style={[
            styles.centerButton,
            {
              backgroundColor: activeColor,
              position: 'absolute',
              left: (screenWidth - 56) / 2, // Center of screen
              top: -28, // Elevated above the tab bar
              zIndex: 10, // Ensure it's on top
            },
          ]}
          onPress={handleQRPress}>
          <AppIcons.qrCode size={28} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabBar: {
    flexDirection: 'row',
    height: 70,
    borderTopWidth: 1,
    // Removed shadows for clean design
  },
  tabContent: {
    flex: 1,
    flexDirection: 'row',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  centerButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    // Removed shadows for clean design
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
});
