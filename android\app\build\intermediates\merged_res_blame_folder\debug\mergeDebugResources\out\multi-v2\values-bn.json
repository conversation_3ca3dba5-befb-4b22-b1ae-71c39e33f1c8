{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,4622,6859,6997,7065,7206,8484,8551,8625,12637,12720,12800,12870,13271,13351,13426,13514,13601,13676,13752,13827,14023,14099,14176,14246", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3119,4694,6926,7060,7140,7269,8546,8620,8697,12715,12795,12865,12944,13346,13421,13509,13596,13671,13747,13822,13917,14094,14171,14241,14314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,12949", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,13031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4816,5229,5331,5440", "endColumns": "105,101,108,105", "endOffsets": "4917,5326,5435,5541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1468,1558,1622,1689,1750,1819,1881,1945,2012,2066,2120,2227,2286,2347,2401,2475,2595,2680,2770,2876,2966,3050,3185,3256,3326,3452,3539,3622,3680,3736,3802,3875,3955,4026,4108,4177,4253,4333,4402,4511,4606,4689,4779,4874,4948,5022,5115,5169,5254,5321,5407,5492,5554,5618,5681,5747,5849,5948,6041,6140,6235,6328,6390,6450,6530,6613,6692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,82,89,63,66,60,68,61,63,66,53,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,125,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,94,92,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1463,1553,1617,1684,1745,1814,1876,1940,2007,2061,2115,2222,2281,2342,2396,2470,2590,2675,2765,2871,2961,3045,3180,3251,3321,3447,3534,3617,3675,3731,3797,3870,3950,4021,4103,4172,4248,4328,4397,4506,4601,4684,4774,4869,4943,5017,5110,5164,5249,5316,5402,5487,5549,5613,5676,5742,5844,5943,6036,6135,6230,6323,6385,6445,6525,6608,6687,6760"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3124,3222,3315,3398,3499,4320,4424,4541,5011,5072,5138,6931,7145,7274,7357,7447,7511,7578,7639,7708,7770,7834,7901,7955,8009,8116,8175,8236,8290,8364,8702,8787,8877,8983,9073,9157,9292,9363,9433,9559,9646,9729,9787,9843,9909,9982,10062,10133,10215,10284,10360,10440,10509,10618,10713,10796,10886,10981,11055,11129,11222,11276,11361,11428,11514,11599,11661,11725,11788,11854,11956,12055,12148,12247,12342,12435,12497,12557,13036,13119,13198", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,82,89,63,66,60,68,61,63,66,53,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,125,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,94,92,61,59,79,82,78,72", "endOffsets": "316,3217,3310,3393,3494,3586,4419,4536,4617,5067,5133,5224,6992,7201,7352,7442,7506,7573,7634,7703,7765,7829,7896,7950,8004,8111,8170,8231,8285,8359,8479,8782,8872,8978,9068,9152,9287,9358,9428,9554,9641,9724,9782,9838,9904,9977,10057,10128,10210,10279,10355,10435,10504,10613,10708,10791,10881,10976,11050,11124,11217,11271,11356,11423,11509,11594,11656,11720,11783,11849,11951,12050,12143,12242,12337,12430,12492,12552,12632,13114,13193,13266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3591,3690,3792,3894,3997,4098,4200,13922", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3685,3787,3889,3992,4093,4195,4315,14018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,261,382,521,655,784,908,1055,1158,1295,1440", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "167,256,377,516,650,779,903,1050,1153,1290,1435,1569"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4699,4922,5546,5667,5806,5940,6069,6193,6340,6443,6580,6725", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "4811,5006,5662,5801,5935,6064,6188,6335,6438,6575,6720,6854"}}]}]}