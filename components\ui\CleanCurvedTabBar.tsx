import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, { Path } from 'react-native-svg';
import { Text } from 'tamagui';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const CleanCurvedTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation
}) => {
  const insets = useSafeAreaInsets();
  const { isDark } = useTheme();

  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const centerButtonBg = isDark ? '#257587' : '#15424E';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  const handleTabPress = (route: any, index: number) => {
    const event = navigation.emit({
      type: 'tabPress',
      target: route.key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      navigation.navigate(route.name);
    }
  };

  const handleQRPress = () => {
    console.log('QR Scanner pressed');
    // Add your QR scanner logic here
  };

  const tabBarHeight = 80;
  const centerButtonSize = 60;
  const curveHeight = 30;

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* SVG Curved Background */}
      <Svg
        width={screenWidth}
        height={tabBarHeight}
        style={styles.svgBackground}
      >
        <Path
          d={`M0,${curveHeight} 
             L${screenWidth * 0.35},${curveHeight} 
             Q${screenWidth * 0.4},${curveHeight} ${screenWidth * 0.42},${curveHeight - 10}
             L${screenWidth * 0.46},${curveHeight - 25}
             Q${screenWidth * 0.5},${curveHeight - 35} ${screenWidth * 0.54},${curveHeight - 25}
             L${screenWidth * 0.58},${curveHeight - 10}
             Q${screenWidth * 0.6},${curveHeight} ${screenWidth * 0.65},${curveHeight}
             L${screenWidth},${curveHeight}
             L${screenWidth},${tabBarHeight}
             L0,${tabBarHeight}
             Z`}
          fill={tabBarBg}
          stroke={borderColor}
          strokeWidth={1}
        />
      </Svg>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {/* Left Tabs */}
        <View style={styles.leftTabs}>
          {state.routes.slice(0, 2).map((route, index) => {
            const isFocused = state.index === index;
            
            return (
              <TouchableOpacity
                key={route.key}
                style={styles.tab}
                onPress={() => handleTabPress(route, index)}
              >
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={24}
                />
                <Text
                  fontSize={12}
                  color={isFocused ? activeColor : inactiveColor}
                  marginTop={4}
                >
                  {getTabLabel(route.name)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Center QR Button */}
        <TouchableOpacity
          style={[styles.centerButton, { backgroundColor: centerButtonBg }]}
          onPress={handleQRPress}
        >
          <AppIcons.qrCode size={28} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Right Tabs */}
        <View style={styles.rightTabs}>
          {state.routes.slice(2, 4).map((route, index) => {
            const actualIndex = index + 2;
            const isFocused = state.index === actualIndex;
            
            return (
              <TouchableOpacity
                key={route.key}
                style={styles.tab}
                onPress={() => handleTabPress(route, actualIndex)}
              >
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={24}
                />
                <Text
                  fontSize={12}
                  color={isFocused ? activeColor : inactiveColor}
                  marginTop={4}
                >
                  {getTabLabel(route.name)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
  svgBackground: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 80,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  leftTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  rightTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
