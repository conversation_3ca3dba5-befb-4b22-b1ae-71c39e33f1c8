import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { CurvedBottomBar } from 'react-native-curved-bottom-bar';
import { Text, View } from 'tamagui';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const CurvedTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation
}) => {
  const { isDark } = useTheme();

  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const centerButtonBg = isDark ? '#257587' : '#15424E';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  const handleCenterPress = () => {
    console.log('QR Code scanner pressed');
    // TODO: Navigate to QR scanner or open modal
  };

  return (
    <CurvedBottomBar.Navigator
      type="UP"
      style={{
        backgroundColor: tabBarBg,
        borderTopColor: borderColor,
        borderTopWidth: 1,
      }}
      shadowStyle={{
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: -2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 5,
      }}
      height={75}
      circleWidth={65}
      bgColor={tabBarBg}
      initialRouteName="index"
      borderTopLeftRight={true}
      renderCircle={({ selectedTab, navigate }) => (
        <Pressable
          style={{
            width: 65,
            height: 65,
            borderRadius: 32.5,
            backgroundColor: centerButtonBg,
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 4,
            },
            shadowOpacity: 0.3,
            shadowRadius: 4.65,
            elevation: 8,
          }}
          onPress={handleCenterPress}
        >
          <AppIcons.qrCode size={26} color="#FFFFFF" />
        </Pressable>
      )}
      tabBar={({ routeName, selectedTab, navigate }) => {
        const isFocused = routeName === selectedTab;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: routeName,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(routeName);
          }
        };

        return (
          <Pressable onPress={onPress} style={styles.tab}>
            <TabBarIcon
              name={routeName}
              focused={isFocused}
              color={isFocused ? activeColor : inactiveColor}
              size={isFocused ? 22 : 20}
            />
            <Text
              fontSize={isFocused ? 12 : 11}
              color={isFocused ? activeColor : inactiveColor}
              fontWeight={isFocused ? '600' : '400'}
              marginTop={2}
            >
              {getTabLabel(routeName)}
            </Text>
          </Pressable>
        );
      }}
    >
      <CurvedBottomBar.Screen
        name="index"
        position="LEFT"
        component={() => <View />}
      />
      <CurvedBottomBar.Screen
        name="calendar"
        position="LEFT"
        component={() => <View />}
      />
      <CurvedBottomBar.Screen
        name="clients"
        position="RIGHT"
        component={() => <View />}
      />
      <CurvedBottomBar.Screen
        name="bookings"
        position="RIGHT"
        component={() => <View />}
      />
    </CurvedBottomBar.Navigator>
  );
};

const styles = StyleSheet.create({
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
});
