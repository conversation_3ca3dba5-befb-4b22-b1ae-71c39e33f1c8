import { Text, View } from '@tamagui/core'
import React, { useState } from 'react'
import { Pressable, ScrollView } from 'react-native'
import { useTheme, useThemeToggle } from '../../theme/DaltiThemeProvider'
import { InputShowcase } from './InputShowcase'

export function ThemeShowcase() {
  const { theme, isDark } = useTheme()
  const toggleTheme = useThemeToggle()
  const [showInputs, setShowInputs] = useState(false)

  if (showInputs) {
    return <InputShowcase />
  }

  return (
    <ScrollView style={{ flex: 1 }}>
      <View padding="$lg" alignItems="center" gap="$lg" backgroundColor="$surface" borderRadius="$card">
        <Text fontSize="$headlineLarge" fontWeight="bold" textAlign="center" color="$color">
          Dalti Custom Theme! 🎨
        </Text>
        
        <Text fontSize="$bodyMedium" color="$colorSecondary" textAlign="center">
          Current theme: {theme} • Using custom design tokens
        </Text>

        <Pressable onPress={toggleTheme}>
          <View 
            backgroundColor="$primary" 
            padding="$md" 
            borderRadius="$button"
            minWidth={120}
            alignItems="center"
          >
            <Text color="$colorOnPrimary" fontWeight="semibold">
              Switch to {isDark ? 'Light' : 'Dark'}
            </Text>
          </View>
        </Pressable>

        <View gap="$md" alignItems="center">
          <Text fontSize="$titleMedium" fontWeight="semibold" color="$color">
            Brand Colors
          </Text>
          <View flexDirection="row" gap="$sm">
            <View backgroundColor="$primary" padding="$md" borderRadius="$md" minWidth={80} alignItems="center">
              <Text color="$colorOnPrimary" fontSize="$labelMedium" fontWeight="semibold">Primary</Text>
            </View>
            <View backgroundColor="$secondary" padding="$md" borderRadius="$md" minWidth={80} alignItems="center">
              <Text color="$colorOnSecondary" fontSize="$labelMedium" fontWeight="semibold">Secondary</Text>
            </View>
            <View backgroundColor="$accent" padding="$md" borderRadius="$md" minWidth={80} alignItems="center">
              <Text color="$colorOnAccent" fontSize="$labelMedium" fontWeight="semibold">Accent</Text>
            </View>
          </View>
        </View>

        <View gap="$md" alignItems="center">
          <Text fontSize="$titleMedium" fontWeight="semibold" color="$color">
            Status Colors
          </Text>
          <View flexDirection="row" gap="$sm">
            <View backgroundColor="$success" padding="$sm" borderRadius="$sm" alignItems="center">
              <Text color="white" fontSize="$labelSmall" fontWeight="semibold">Success</Text>
            </View>
            <View backgroundColor="$warning" padding="$sm" borderRadius="$sm" alignItems="center">
              <Text color="white" fontSize="$labelSmall" fontWeight="semibold">Warning</Text>
            </View>
            <View backgroundColor="$error" padding="$sm" borderRadius="$sm" alignItems="center">
              <Text color="white" fontSize="$labelSmall" fontWeight="semibold">Error</Text>
            </View>
          </View>
        </View>

        <View gap="$sm" alignItems="center">
          <Text fontSize="$titleMedium" fontWeight="semibold" color="$color">
            Typography Scale
          </Text>
          <Text fontSize="$displaySmall" fontWeight="bold" color="$color">Display</Text>
          <Text fontSize="$headlineMedium" fontWeight="semibold" color="$color">Headline</Text>
          <Text fontSize="$titleLarge" fontWeight="semibold" color="$color">Title</Text>
          <Text fontSize="$bodyLarge" color="$color">Body Text</Text>
          <Text fontSize="$labelMedium" fontWeight="semibold" color="$colorSecondary">Label</Text>
        </View>

        <Pressable onPress={() => setShowInputs(true)}>
          <View
            backgroundColor="$secondary"
            padding="$md"
            borderRadius="$button"
            minWidth={160}
            alignItems="center"
          >
            <Text color="$colorOnSecondary" fontWeight="semibold">
              View Input Components
            </Text>
          </View>
        </Pressable>
      </View>
    </ScrollView>
  )
}
