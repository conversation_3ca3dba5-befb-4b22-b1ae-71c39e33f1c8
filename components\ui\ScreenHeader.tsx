import { View } from '@tamagui/core'
import React from 'react'
import { Pressable } from 'react-native'
import { useTheme, useThemeToggle } from '../../theme/DaltiThemeProvider'
import { AppIcons } from './Icons'

interface ScreenHeaderProps {
  showBackButton?: boolean
  onBackPress?: () => void
  showLanguageToggle?: boolean
  showThemeToggle?: boolean
}

export const ScreenHeader: React.FC<ScreenHeaderProps> = ({
  showBackButton = false,
  onBackPress,
  showLanguageToggle = true,
  showThemeToggle = true,
}) => {
  const { isDark } = useTheme()
  const toggleTheme = useThemeToggle()

  const iconColor = isDark ? '#B8BCC8' : '#666'

  return (
    <View
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      paddingHorizontal="$lg"
      paddingTop="$xl"
      paddingBottom="$md"
    >
      {/* Left side */}
      <View width={40} height={40} justifyContent="center">
        {showBackButton && (
          <Pressable
            onPress={onBackPress}
            style={{
              width: 40,
              height: 40,
              borderRadius: 12,
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <AppIcons.back size={20} color={iconColor} />
          </Pressable>
        )}
      </View>

      {/* Right side */}
      <View flexDirection="row" gap="$sm">
        {showLanguageToggle && (
          <Pressable
            style={{
              width: 40,
              height: 40,
              borderRadius: 12,
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <AppIcons.search size={20} color={iconColor} />
          </Pressable>
        )}

        {showThemeToggle && (
          <Pressable
            onPress={toggleTheme}
            style={{
              width: 40,
              height: 40,
              borderRadius: 12,
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {isDark ? (
              <AppIcons.sun size={20} color={iconColor} />
            ) : (
              <AppIcons.moon size={20} color={iconColor} />
            )}
          </Pressable>
        )}
      </View>
    </View>
  )
}
