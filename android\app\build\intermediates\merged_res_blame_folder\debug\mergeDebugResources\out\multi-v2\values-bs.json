{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1507,1615,1675,1741,1797,1868,1928,1995,2067,2128,2182,2301,2358,2420,2474,2549,2673,2761,2838,2932,3016,3099,3244,3329,3415,3548,3636,3714,3768,3822,3888,3962,4040,4111,4193,4265,4342,4415,4485,4594,4687,4759,4851,4947,5021,5097,5193,5246,5328,5395,5482,5569,5631,5695,5758,5827,5935,6040,6141,6244,6342,6438,6496,6554,6634,6720,6796", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,79,107,59,65,55,70,59,66,71,60,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,97,95,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1502,1610,1670,1736,1792,1863,1923,1990,2062,2123,2177,2296,2353,2415,2469,2544,2668,2756,2833,2927,3011,3094,3239,3324,3410,3543,3631,3709,3763,3817,3883,3957,4035,4106,4188,4260,4337,4410,4480,4589,4682,4754,4846,4942,5016,5092,5188,5241,5323,5390,5477,5564,5626,5690,5753,5822,5930,6035,6136,6239,6337,6433,6491,6549,6629,6715,6791,6868"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,52,53,54,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3107,3186,3266,3348,3450,4269,4365,4491,4878,4940,5006,6682,6759,6822,6902,7010,7070,7136,7192,7263,7323,7390,7462,7523,7577,7696,7753,7815,7869,7944,8068,8156,8233,8327,8411,8494,8639,8724,8810,8943,9031,9109,9163,9217,9283,9357,9435,9506,9588,9660,9737,9810,9880,9989,10082,10154,10246,10342,10416,10492,10588,10641,10723,10790,10877,10964,11026,11090,11153,11222,11330,11435,11536,11639,11737,11833,11891,11949,12116,12202,12278", "endLines": "6,34,35,36,37,38,46,47,48,52,53,54,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,79,107,59,65,55,70,59,66,71,60,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,97,95,57,57,79,85,75,76", "endOffsets": "372,3181,3261,3343,3445,3539,4360,4486,4567,4935,5001,5093,6754,6817,6897,7005,7065,7131,7187,7258,7318,7385,7457,7518,7572,7691,7748,7810,7864,7939,8063,8151,8228,8322,8406,8489,8634,8719,8805,8938,9026,9104,9158,9212,9278,9352,9430,9501,9583,9655,9732,9805,9875,9984,10077,10149,10241,10337,10411,10487,10583,10636,10718,10785,10872,10959,11021,11085,11148,11217,11325,11430,11531,11634,11732,11828,11886,11944,12024,12197,12273,12350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,380,512,643,771,901,1035,1136,1271,1404", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "159,252,375,507,638,766,896,1030,1131,1266,1399,1522"}, "to": {"startLines": "49,51,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4572,4785,5412,5535,5667,5798,5926,6056,6190,6291,6426,6559", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "4676,4873,5530,5662,5793,5921,6051,6185,6286,6421,6554,6677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3642,3744,3842,3946,4050,4152,12355", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3637,3739,3837,3941,4045,4147,4264,12451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2520,2625,2739,2842,3011,12029", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2515,2620,2734,2837,3006,3102,12111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "50,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4681,5098,5198,5312", "endColumns": "103,99,113,99", "endOffsets": "4780,5193,5307,5407"}}]}]}