{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,12818", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,12894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4719,5117,5218,5327", "endColumns": "100,100,108,100", "endOffsets": "4815,5213,5322,5423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,342,421,488,555,629,705,785,866,934,1013,1091,1166,1245,1325,1405,1476,1547,1646,1718,1793,1862", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "119,198,269,337,416,483,550,624,700,780,861,929,1008,1086,1161,1240,1320,1400,1471,1542,1641,1713,1788,1857,1930"}, "to": {"startLines": "33,49,69,71,72,74,92,93,94,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3001,4527,6723,6861,6929,7067,8371,8438,8512,12510,12590,12671,12739,13133,13211,13286,13365,13445,13525,13596,13667,13867,13939,14014,14083", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "3065,4601,6789,6924,7003,7129,8433,8507,8583,12585,12666,12734,12813,13206,13281,13360,13440,13520,13591,13662,13761,13934,14009,14078,14151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,353,455,557,655,777", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "148,251,348,450,552,650,772,873"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3502,3600,3703,3800,3902,4004,4102,13766", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "3595,3698,3795,3897,3999,4097,4219,13862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1060,1123,1214,1281,1340,1428,1518,1581,1646,1710,1779,1841,1910,1979,2035,2089,2204,2262,2323,2377,2450,2577,2663,2745,2844,2929,3013,3146,3221,3297,3430,3516,3597,3651,3703,3769,3842,3922,3993,4073,4144,4220,4299,4368,4475,4571,4649,4744,4838,4912,4987,5081,5132,5214,5281,5368,5458,5520,5584,5647,5714,5816,5921,6018,6120,6215,6307,6365,6421,6499,6585,6660", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,87,89,62,64,63,68,61,68,68,55,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,93,73,74,93,50,81,66,86,89,61,63,62,66,101,104,96,101,94,91,57,55,77,85,74,72", "endOffsets": "263,337,409,491,597,695,794,914,998,1055,1118,1209,1276,1335,1423,1513,1576,1641,1705,1774,1836,1905,1974,2030,2084,2199,2257,2318,2372,2445,2572,2658,2740,2839,2924,3008,3141,3216,3292,3425,3511,3592,3646,3698,3764,3837,3917,3988,4068,4139,4215,4294,4363,4470,4566,4644,4739,4833,4907,4982,5076,5127,5209,5276,5363,5453,5515,5579,5642,5709,5811,5916,6013,6115,6210,6302,6360,6416,6494,6580,6655,6728"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3144,3216,3298,3404,4224,4323,4443,4906,4963,5026,6794,7008,7134,7222,7312,7375,7440,7504,7573,7635,7704,7773,7829,7883,7998,8056,8117,8171,8244,8588,8674,8756,8855,8940,9024,9157,9232,9308,9441,9527,9608,9662,9714,9780,9853,9933,10004,10084,10155,10231,10310,10379,10486,10582,10660,10755,10849,10923,10998,11092,11143,11225,11292,11379,11469,11531,11595,11658,11725,11827,11932,12029,12131,12226,12318,12376,12432,12899,12985,13060", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,87,89,62,64,63,68,61,68,68,55,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,93,73,74,93,50,81,66,86,89,61,63,62,66,101,104,96,101,94,91,57,55,77,85,74,72", "endOffsets": "313,3139,3211,3293,3399,3497,4318,4438,4522,4958,5021,5112,6856,7062,7217,7307,7370,7435,7499,7568,7630,7699,7768,7824,7878,7993,8051,8112,8166,8239,8366,8669,8751,8850,8935,9019,9152,9227,9303,9436,9522,9603,9657,9709,9775,9848,9928,9999,10079,10150,10226,10305,10374,10481,10577,10655,10750,10844,10918,10993,11087,11138,11220,11287,11374,11464,11526,11590,11653,11720,11822,11927,12024,12126,12221,12313,12371,12427,12505,12980,13055,13128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,254,379,506,637,780,915,1057,1154,1291,1429", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "163,249,374,501,632,775,910,1052,1149,1286,1424,1544"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4606,4820,5428,5553,5680,5811,5954,6089,6231,6328,6465,6603", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "4714,4901,5548,5675,5806,5949,6084,6226,6323,6460,6598,6718"}}]}]}