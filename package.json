{"name": "dalti_provider_2", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@bottom-tabs/react-navigation": "^0.11.1", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tamagui/babel-plugin": "^1.132.23", "@tamagui/config": "^1.132.23", "@tamagui/core": "^1.132.23", "@tamagui/font-inter": "^1.132.23", "@tamagui/input": "^1.132.23", "@tanstack/react-query": "^5.87.1", "axios": "^1.11.0", "expo": "~53.0.22", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-bottom-tabs": "^0.11.1", "react-native-curved-bottom-bar": "^3.5.1", "react-native-gesture-handler": "~2.24.0", "react-native-keychain": "^9.2.3", "react-native-mmkv": "^2.12.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.13.0", "react-native-svg-transformer": "^1.5.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tamagui": "^1.132.23", "zod": "^3.25.76", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-import-meta": "^7.10.4", "@tanstack/eslint-plugin-query": "^5.86.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native-keychain": "^3.0.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "~29.7.0", "jest-environment-jsdom": "^30.1.2", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}