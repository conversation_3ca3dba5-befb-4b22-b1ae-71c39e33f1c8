import { Tabs } from 'expo-router';
import React from 'react';

import { AppIcons } from '../../components/ui/Icons';
import { NativeBottomTabBar } from '../../components/ui/NativeBottomTabBar';

export default function TabLayout() {
  return (
    <Tabs
      tabBar={(props) => <NativeBottomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
        tabBarStyle: { display: 'none' }, // Hide default tab bar
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <AppIcons.grid size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
          tabBarIcon: ({ color, size }) => (
            <AppIcons.calendar size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="clients"
        options={{
          title: 'Clients',
          tabBarIcon: ({ color, size }) => (
            <AppIcons.people size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="bookings"
        options={{
          title: 'Bookings',
          tabBarIcon: ({ color, size }) => (
            <AppIcons.bookmarks size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}