module.exports = function (api) {
  api.cache(true)
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // NOTE: this is only necessary if you are using reanimated for animations
      'react-native-reanimated/plugin',
      // Add syntax support for import.meta
      '@babel/plugin-syntax-import-meta',
      // Transform import.meta for web compatibility
      function(babel) {
        const { types: t } = babel;
        return {
          visitor: {
            MetaProperty(path) {
              if (path.node.meta.name === 'import' && path.node.property.name === 'meta') {
                const replacement = t.objectExpression([
                  t.objectProperty(
                    t.identifier('url'),
                    t.conditionalExpression(
                      t.binaryExpression('!==', t.unaryExpression('typeof', t.identifier('window')), t.stringLiteral('undefined')),
                      t.memberExpression(
                        t.memberExpression(t.identifier('window'), t.identifier('location')),
                        t.identifier('href')
                      ),
                      t.stringLiteral('file:///')
                    )
                  ),
                  t.objectProperty(
                    t.identifier('env'),
                    t.conditionalExpression(
                      t.binaryExpression('!==', t.unaryExpression('typeof', t.identifier('process')), t.stringLiteral('undefined')),
                      t.memberExpression(t.identifier('process'), t.identifier('env')),
                      t.objectExpression([])
                    )
                  )
                ]);
                path.replaceWith(replacement);
              }
            }
          }
        };
      }
    ],
  }
}
