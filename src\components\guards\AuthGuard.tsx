import { useRouter, useSegments } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { View } from 'tamagui';
import { useAuthStore } from '../../stores/auth.store';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const segments = useSegments();
  const { isAuthenticated, isLoading, isInitialized, initialize } = useAuthStore();
  const [isRouterReady, setIsRouterReady] = useState(false);

  useEffect(() => {
    // Initialize auth state on app start
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized, initialize]);

  useEffect(() => {
    // Wait for router to be ready
    const timer = setTimeout(() => {
      setIsRouterReady(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isInitialized || isLoading || !isRouterReady) return;

    const inAuthGroup = segments[0] === '(auth)';
    const inTabsGroup = segments[0] === '(tabs)';
    const isOnAuthScreen = ['login', 'register', 'forgot-password', 'password-reset-otp', 'reset-password', 'otp-verification', 'welcome'].includes(segments[0] || '');

    if (isAuthenticated) {
      // User is authenticated
      if (inAuthGroup || isOnAuthScreen) {
        // Redirect to main app if on auth screens
        router.replace('/(tabs)');
      }
    } else {
      // User is not authenticated
      if (inTabsGroup || (!inAuthGroup && !isOnAuthScreen)) {
        // Redirect to welcome/login if trying to access protected screens
        router.replace('/welcome');
      }
    }
  }, [isAuthenticated, isInitialized, isLoading, isRouterReady, segments, router]);

  // Show loading spinner while initializing
  if (!isInitialized || isLoading || !isRouterReady) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return <>{children}</>;
};

// Higher-order component for protecting individual screens
export const withAuthGuard = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireAuth?: boolean;
    redirectTo?: string;
  }
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const router = useRouter();
    const { isAuthenticated, isInitialized, isLoading } = useAuthStore();
    const requireAuth = options?.requireAuth ?? true;
    const redirectTo = options?.redirectTo ?? '/welcome';

    useEffect(() => {
      if (!isInitialized || isLoading) return;

      if (requireAuth && !isAuthenticated) {
        router.replace(redirectTo);
      } else if (!requireAuth && isAuthenticated) {
        router.replace('/(tabs)');
      }
    }, [isAuthenticated, isInitialized, isLoading, router, requireAuth, redirectTo]);

    if (!isInitialized || isLoading) {
      return (
        <View flex={1} justifyContent="center" alignItems="center">
          <LoadingSpinner size="large" />
        </View>
      );
    }

    if (requireAuth && !isAuthenticated) {
      return null; // Will redirect
    }

    if (!requireAuth && isAuthenticated) {
      return null; // Will redirect
    }

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for checking authentication status in components
export const useAuthGuard = () => {
  const { isAuthenticated, isLoading, isInitialized, user, provider } = useAuthStore();
  const router = useRouter();

  const requireAuth = (redirectTo: string = '/welcome') => {
    if (isInitialized && !isLoading && !isAuthenticated) {
      router.replace(redirectTo);
      return false;
    }
    return isAuthenticated;
  };

  const requireGuest = (redirectTo: string = '/(tabs)') => {
    if (isInitialized && !isLoading && isAuthenticated) {
      router.replace(redirectTo);
      return false;
    }
    return !isAuthenticated;
  };

  const requireSetupComplete = (redirectTo: string = '/setup') => {
    if (isAuthenticated && provider && !provider.isSetupComplete) {
      router.replace(redirectTo);
      return false;
    }
    return true;
  };

  return {
    isAuthenticated,
    isLoading,
    isInitialized,
    user,
    provider,
    requireAuth,
    requireGuest,
    requireSetupComplete,
  };
};
