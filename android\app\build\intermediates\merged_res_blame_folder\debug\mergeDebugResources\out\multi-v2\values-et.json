{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3627,3729,3827,3930,4036,4141,13691", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3622,3724,3822,3925,4031,4136,4256,13787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,253,371,500,629,758,889,1020,1119,1258,1393", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "158,248,366,495,624,753,884,1015,1114,1253,1388,1505"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4634,4842,5447,5565,5694,5823,5952,6083,6214,6313,6452,6587", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "4737,4927,5560,5689,5818,5947,6078,6209,6308,6447,6582,6699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4742,5141,5241,5348", "endColumns": "99,99,106,98", "endOffsets": "4837,5236,5343,5442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1407,1494,1558,1622,1681,1753,1817,1885,1956,2014,2068,2187,2247,2308,2362,2435,2568,2652,2729,2822,2902,2995,3133,3213,3292,3418,3506,3585,3640,3691,3757,3830,3909,3980,4059,4132,4207,4281,4353,4466,4554,4631,4722,4814,4888,4962,5053,5107,5189,5258,5341,5427,5489,5553,5616,5684,5787,5890,5987,6088,6178,6266,6325,6380,6461,6550,6627", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,78,86,63,63,58,71,63,67,70,57,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,89,87,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1402,1489,1553,1617,1676,1748,1812,1880,1951,2009,2063,2182,2242,2303,2357,2430,2563,2647,2724,2817,2897,2990,3128,3208,3287,3413,3501,3580,3635,3686,3752,3825,3904,3975,4054,4127,4202,4276,4348,4461,4549,4626,4717,4809,4883,4957,5048,5102,5184,5253,5336,5422,5484,5548,5611,5679,5782,5885,5982,6083,6173,6261,6320,6375,6456,6545,6622,6700"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3189,3268,3353,3445,4261,4360,4477,4932,4992,5056,6777,6993,7057,7136,7223,7287,7351,7410,7482,7546,7614,7685,7743,7797,7916,7976,8037,8091,8164,8518,8602,8679,8772,8852,8945,9083,9163,9242,9368,9456,9535,9590,9641,9707,9780,9859,9930,10009,10082,10157,10231,10303,10416,10504,10581,10672,10764,10838,10912,11003,11057,11139,11208,11291,11377,11439,11503,11566,11634,11737,11840,11937,12038,12128,12216,12275,12330,12817,12906,12983", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,78,86,63,63,58,71,63,67,70,57,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,89,87,58,54,80,88,76,77", "endOffsets": "311,3184,3263,3348,3440,3527,4355,4472,4554,4987,5051,5136,6840,7052,7131,7218,7282,7346,7405,7477,7541,7609,7680,7738,7792,7911,7971,8032,8086,8159,8292,8597,8674,8767,8847,8940,9078,9158,9237,9363,9451,9530,9585,9636,9702,9775,9854,9925,10004,10077,10152,10226,10298,10411,10499,10576,10667,10759,10833,10907,10998,11052,11134,11203,11286,11372,11434,11498,11561,11629,11732,11835,11932,12033,12123,12211,12270,12325,12406,12901,12978,13056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,12734", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,12812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,201,274,342,422,490,565,643,725,813,887,966,1047,1124,1207,1290,1368,1442,1513,1596,1671,1755,1825", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "121,196,269,337,417,485,560,638,720,808,882,961,1042,1119,1202,1285,1363,1437,1508,1591,1666,1750,1820,1899"}, "to": {"startLines": "33,49,69,71,72,91,92,93,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,4559,6704,6845,6913,8297,8365,8440,12411,12493,12581,12655,13061,13142,13219,13302,13385,13463,13537,13608,13792,13867,13951,14021", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "3104,4629,6772,6908,6988,8360,8435,8513,12488,12576,12650,12729,13137,13214,13297,13380,13458,13532,13603,13686,13862,13946,14016,14095"}}]}]}