import { useRouter } from 'expo-router'
import React, { useState } from 'react'
import { Alert } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import {
    Card,
    H4,
    Input,
    Label,
    ScrollView,
    Separator,
    Text,
    TextArea,
    View,
    XStack,
    YStack
} from 'tamagui'
import { AppBar } from '../components/ui/AppBar'
import { AppIcons } from '../components/ui/Icons'

// Form Section Component
interface FormSectionProps {
  title: string
  icon: React.ReactNode
  children: React.ReactNode
}

const FormSection: React.FC<FormSectionProps> = ({ title, icon, children }) => {
  return (
    <Card
      elevate
      size="$4"
      bordered
      animation="bouncy"
      scale={0.9}
      hoverStyle={{ scale: 0.925 }}
      pressStyle={{ scale: 0.875 }}
      margin="$4"
      padding="$0"
    >
      <Card.Header padded>
        <XStack alignItems="center" space="$3">
          {icon}
          <H4 color="$color">{title}</H4>
        </XStack>
        <Separator marginVertical="$3" />
      </Card.Header>

      <YStack space="$3" padding="$4">
        {children}
      </YStack>
    </Card>
  )
}

// Input Field Component using Tamagui
interface InputFieldProps {
  icon: React.ReactNode
  placeholder: string
  value: string
  onChangeText: (text: string) => void
  multiline?: boolean
  required?: boolean
}

const InputField: React.FC<InputFieldProps> = ({
  icon,
  placeholder,
  value,
  onChangeText,
  multiline = false,
  required = false
}) => {
  const placeholderText = `${placeholder}${required ? ' *' : ''}`

  return (
    <YStack space="$2">
      <Label htmlFor={placeholder} color="$color" fontSize="$3" fontWeight="600">
        <XStack alignItems="center" space="$2">
          {icon}
          <Text>{placeholderText}</Text>
        </XStack>
      </Label>

      {multiline ? (
        <TextArea
          id={placeholder}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholderText}
          size="$4"
          borderWidth={2}
          borderColor="$borderColor"
          focusStyle={{
            borderColor: '$blue10',
            borderWidth: 2,
          }}
          minHeight={100}
        />
      ) : (
        <Input
          id={placeholder}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholderText}
          size="$4"
          borderWidth={2}
          borderColor="$borderColor"
          focusStyle={{
            borderColor: '$blue10',
            borderWidth: 2,
          }}
        />
      )}
    </YStack>
  )
}

export default function NewCustomerScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()


  // Form state
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [nationalId, setNationalId] = useState('')
  const [contactNotes, setContactNotes] = useState('')
  const [additionalNotes, setAdditionalNotes] = useState('')

  const iconColor = '#257587'

  const handleBack = () => {
    router.back()
  }

  const handleSave = () => {
    // Basic validation
    if (!firstName.trim() || !lastName.trim()) {
      Alert.alert('Error', 'First Name and Last Name are required')
      return
    }

    if (!email.trim() && !phoneNumber.trim()) {
      Alert.alert('Error', 'Please provide either an email or phone number')
      return
    }

    // TODO: Implement customer creation API call
    console.log('Creating customer:', {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim(),
      phoneNumber: phoneNumber.trim(),
      nationalId: nationalId.trim(),
      contactNotes: contactNotes.trim(),
      additionalNotes: additionalNotes.trim(),
    })

    Alert.alert('Success', 'Customer created successfully', [
      {
        text: 'OK',
        onPress: () => router.back(),
      },
    ])
  }

  return (
    <View
      flex={1}
      backgroundColor="$background"
      style={{ paddingTop: insets.top }}
    >
      <AppBar
        title="New Customer"
        startIcon={{
          icon: <AppIcons.back size={20} />,
          onPress: handleBack,
        }}
        endIcon={{
          icon: <AppIcons.checkmark size={20} />,
          onPress: handleSave,
        }}
      />

      <ScrollView
        flex={1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 100,
        }}
      >
        {/* Basic Information Section */}
        <FormSection
          title="Basic Information"
          icon={<AppIcons.person size={20} color={iconColor} />}
        >
          <XStack space="$4">
            <View flex={1}>
              <InputField
                icon={<AppIcons.person size={20} color={iconColor} />}
                placeholder="First Name"
                value={firstName}
                onChangeText={setFirstName}
                required={true}
              />
            </View>
            <View flex={1}>
              <InputField
                icon={<AppIcons.person size={20} color={iconColor} />}
                placeholder="Last Name"
                value={lastName}
                onChangeText={setLastName}
                required={true}
              />
            </View>
          </XStack>
        </FormSection>

        {/* Contact Information Section */}
        <FormSection
          title="Contact Information"
          icon={<AppIcons.call size={20} color={iconColor} />}
        >
          <InputField
            icon={<AppIcons.mail size={20} color={iconColor} />}
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
          />
          
          <InputField
            icon={<AppIcons.call size={20} color={iconColor} />}
            placeholder="Phone Number"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
          />
          
          <InputField
            icon={<AppIcons.card size={20} color={iconColor} />}
            placeholder="National ID"
            value={nationalId}
            onChangeText={setNationalId}
          />
          
          <InputField
            icon={<AppIcons.document size={20} color={iconColor} />}
            placeholder="Notes"
            value={contactNotes}
            onChangeText={setContactNotes}
            multiline={true}
          />
        </FormSection>

        {/* Additional Information Section */}
        <FormSection
          title="Additional Information"
          icon={<AppIcons.helpCircle size={20} color={iconColor} />}
        >
          <InputField
            icon={<AppIcons.document size={20} color={iconColor} />}
            placeholder="Notes"
            value={additionalNotes}
            onChangeText={setAdditionalNotes}
            multiline={true}
          />
        </FormSection>
      </ScrollView>
    </View>
  )
}
