import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Text, View } from 'tamagui';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

interface TabBarIconProps {
  routeName: string;
  index: number;
  selectedIndex: number;
  onPress: () => void;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ routeName, index, selectedIndex, onPress }) => {
  const { isDark } = useTheme();
  const isSelected = index === selectedIndex;
  
  // Theme-aware colors
  const activeColor = isDark ? '#257587' : '#15424E';
  const inactiveColor = isDark ? '#B8BCC8' : '#7F8C8D';
  const iconColor = isSelected ? activeColor : inactiveColor;

  const getIcon = () => {
    const size = isSelected ? 24 : 22;
    switch (routeName) {
      case 'index':
        return <AppIcons.grid size={size} color={iconColor} />;
      case 'calendar':
        return <AppIcons.calendar size={size} color={iconColor} />;
      case 'clients':
        return <AppIcons.people size={size} color={iconColor} />;
      case 'bookings':
        return <AppIcons.bookmarks size={size} color={iconColor} />;
      default:
        return <AppIcons.home size={size} color={iconColor} />;
    }
  };

  const getLabel = () => {
    switch (routeName) {
      case 'index':
        return 'Home';
      case 'calendar':
        return 'Calendar';
      case 'clients':
        return 'Clients';
      case 'bookings':
        return 'Bookings';
      default:
        return routeName;
    }
  };

  return (
    <Pressable onPress={onPress} style={styles.tabItem}>
      <Animated.View style={[styles.tabContent]}>
        {getIcon()}
        <Text
          fontSize={isSelected ? 12 : 11}
          color={iconColor}
          fontWeight={isSelected ? '600' : '400'}
          marginTop={2}
        >
          {getLabel()}
        </Text>
      </Animated.View>
    </Pressable>
  );
};

interface ModernTabBarProps {
  selectedIndex: number;
  onTabPress: (index: number) => void;
  onCenterPress: () => void;
  routes: string[];
}

export const ModernTabBar: React.FC<ModernTabBarProps> = ({
  selectedIndex,
  onTabPress,
  onCenterPress,
  routes,
}) => {
  const { isDark } = useTheme();
  const insets = useSafeAreaInsets();
  
  // Theme-aware colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const centerButtonBg = isDark ? '#257587' : '#15424E';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';
  const shadowColor = isDark ? '#000' : '#000';

  // Animation values
  const centerButtonScale = useSharedValue(1);

  const centerButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: centerButtonScale.value }],
  }));

  const handleCenterPress = () => {
    centerButtonScale.value = withSpring(0.9, { duration: 100 }, () => {
      centerButtonScale.value = withSpring(1, { duration: 100 });
    });
    onCenterPress();
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: tabBarBg,
          borderTopColor: borderColor,
          paddingBottom: insets.bottom,
          shadowColor,
        },
      ]}
    >
      {/* Background with notch */}
      <View style={styles.tabBarContent}>
        {/* Left tabs */}
        <View style={styles.leftTabs}>
          {routes.slice(0, 2).map((route, index) => (
            <TabBarIcon
              key={route}
              routeName={route}
              index={index}
              selectedIndex={selectedIndex}
              onPress={() => onTabPress(index)}
            />
          ))}
        </View>

        {/* Center button with notch effect */}
        <View style={styles.centerContainer}>
          <View
            style={[
              styles.centerNotch,
              { backgroundColor: tabBarBg }
            ]}
          />
          <Animated.View style={[centerButtonAnimatedStyle]}>
            <Pressable
              onPress={handleCenterPress}
              style={[
                styles.centerButton,
                {
                  backgroundColor: centerButtonBg,
                  shadowColor,
                },
              ]}
            >
              <AppIcons.qrCode size={26} color="#FFFFFF" />
            </Pressable>
          </Animated.View>
        </View>

        {/* Right tabs */}
        <View style={styles.rightTabs}>
          {routes.slice(2, 4).map((route, index) => (
            <TabBarIcon
              key={route}
              routeName={route}
              index={index + 2}
              selectedIndex={selectedIndex}
              onPress={() => onTabPress(index + 2)}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  tabBarContent: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 70,
    paddingHorizontal: 20,
  },
  leftTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  rightTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  centerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    marginTop: -25,
  },
  centerNotch: {
    position: 'absolute',
    top: 25,
    width: 80,
    height: 30,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
