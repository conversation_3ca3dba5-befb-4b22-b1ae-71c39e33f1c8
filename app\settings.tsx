import { useRouter } from 'expo-router'
import React from 'react'
import { <PERSON><PERSON>, ScrollView, Switch } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Text, View } from 'tamagui'
import { AppBar } from '../components/ui/AppBar'
import { AppIcons } from '../components/ui/Icons'
import { useAuthStore } from '../src/stores/auth.store'
import { useTheme } from '../theme/DaltiThemeProvider'

// Settings Item Component
interface SettingsItemProps {
  icon: React.ReactNode
  title: string
  subtitle?: string
  onPress?: () => void
  showChevron?: boolean
  rightElement?: React.ReactNode
  iconBackgroundColor?: string
}

const SettingsItem: React.FC<SettingsItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showChevron = true,
  rightElement,
  iconBackgroundColor
}) => {
  const { isDark } = useTheme()
  
  const itemBg = isDark ? '#1C2127' : '#FFFFFF'
  const titleColor = isDark ? '#F8F9FA' : '#2C3E50'
  const subtitleColor = isDark ? '#B8BCC8' : '#7F8C8D'
  const defaultIconBg = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'

  return (
    <View
      backgroundColor={itemBg}
      borderRadius="$md"
      marginBottom="$sm"
      onPress={onPress}
      pressStyle={{ opacity: 0.7 }}
      cursor={onPress ? 'pointer' : 'default'}
    >
      <View
        flexDirection="row"
        alignItems="center"
        padding="$lg"
        gap="$md"
      >
        {/* Icon Container */}
        <View
          width={40}
          height={40}
          borderRadius="$md"
          backgroundColor={iconBackgroundColor || defaultIconBg}
          alignItems="center"
          justifyContent="center"
        >
          {icon}
        </View>

        {/* Content */}
        <View flex={1}>
          <Text
            fontSize="$bodyLarge"
            fontWeight="500"
            color={titleColor}
            marginBottom={subtitle ? "$xs" : 0}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              fontSize="$bodyMedium"
              color={subtitleColor}
            >
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right Element */}
        {rightElement || (showChevron && (
          <AppIcons.chevronDown 
            size={20} 
            color={subtitleColor}
            style={{ transform: [{ rotate: '-90deg' }] }}
          />
        ))}
      </View>
    </View>
  )
}

// Settings Section Component
interface SettingsSectionProps {
  title: string
  children: React.ReactNode
}

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, children }) => {
  const { isDark } = useTheme()
  const sectionTitleColor = isDark ? '#257587' : '#15424E'

  return (
    <View marginBottom="$xl">
      <Text
        fontSize="$titleMedium"
        fontWeight="600"
        color={sectionTitleColor}
        marginBottom="$md"
        paddingHorizontal="$lg"
      >
        {title}
      </Text>
      <View paddingHorizontal="$lg">
        {children}
      </View>
    </View>
  )
}

export default function SettingsScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { isDark, toggleTheme } = useTheme()
  const { logout, isLoading } = useAuthStore()

  const screenBg = isDark ? '#0F1419' : '#FAFAFA'

  const handleBack = () => {
    router.back()
  }

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout()
              // Navigation will be handled by AuthGuard
              console.log('Logout successful')
            } catch (error) {
              console.error('Logout error:', error)
              Alert.alert('Error', 'Failed to logout. Please try again.')
            }
          },
        },
      ]
    )
  }

  return (
    <View flex={1} backgroundColor={screenBg} style={{ paddingTop: insets.top }}>
      <AppBar
        title="Settings"
        startIcon={{
          icon: <AppIcons.back size={20} />,
          onPress: handleBack,
        }}
      />

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Management Section */}
        <SettingsSection title="Management">
          <SettingsItem
            icon={<AppIcons.business size={20} color="#257587" />}
            title="Services"
            subtitle="Manage your service offerings"
            onPress={() => console.log('Services pressed')}
            iconBackgroundColor="rgba(37, 117, 135, 0.1)"
          />
          
          <SettingsItem
            icon={<AppIcons.location size={20} color="#257587" />}
            title="Locations"
            subtitle="Configure business locations"
            onPress={() => console.log('Locations pressed')}
            iconBackgroundColor="rgba(37, 117, 135, 0.1)"
          />
          
          <SettingsItem
            icon={<AppIcons.calendar size={20} color="#257587" />}
            title="Queues"
            subtitle="Set up appointment queues"
            onPress={() => console.log('Queues pressed')}
            iconBackgroundColor="rgba(37, 117, 135, 0.1)"
          />
        </SettingsSection>

        {/* App Settings Section */}
        <SettingsSection title="App Settings">
          <SettingsItem
            icon={<AppIcons.moon size={20} color="#666" />}
            title="Theme"
            subtitle={isDark ? "Dark" : "Light"}
            showChevron={false}
            rightElement={
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: '#E1E8ED', true: '#257587' }}
                thumbColor={isDark ? '#FFFFFF' : '#FFFFFF'}
              />
            }
          />
          
          <SettingsItem
            icon={<AppIcons.globe size={20} color="#666" />}
            title="Language"
            subtitle="English"
            onPress={() => console.log('Language pressed')}
          />

          <SettingsItem
            icon={<AppIcons.helpCircle size={20} color="#666" />}
            title="Help & Support"
            subtitle="Get help and support"
            onPress={() => console.log('Help pressed')}
          />
        </SettingsSection>

        {/* Account Section */}
        <SettingsSection title="Account">
          <SettingsItem
            icon={<AppIcons.logOut size={20} color="#E74C3C" />}
            title="Logout"
            subtitle="Sign out of your account"
            onPress={handleLogout}
            iconBackgroundColor="rgba(231, 76, 60, 0.1)"
          />
        </SettingsSection>

        {/* Bottom spacing */}
        <View height={100} />
      </ScrollView>
    </View>
  )
}
