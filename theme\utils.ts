import { tokens } from './tokens'

// Animation utilities
export const animations = {
  // Duration tokens
  duration: {
    instant: 0,
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 750,
  },
  
  // Easing functions
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    material: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
    materialDecelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
    materialAccelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
  },
}

// Elevation/Shadow utilities
export const shadows = {
  none: 'none',
  sm: '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
  md: '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)',
  lg: '0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23)',
  xl: '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)',
  '2xl': '0px 19px 38px rgba(0, 0, 0, 0.30), 0px 15px 12px rgba(0, 0, 0, 0.22)',
  
  // Component-specific shadows
  card: '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
  button: '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)',
  fab: '0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23)',
  modal: '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)',
  drawer: '0px 19px 38px rgba(0, 0, 0, 0.30), 0px 15px 12px rgba(0, 0, 0, 0.22)',
}

// Gradient utilities
export const gradients = {
  primary: 'linear-gradient(135deg, #15424E 0%, #0D3339 100%)',
  secondary: 'linear-gradient(135deg, #4ECDC4 0%, #3ABAB3 100%)',
  accent: 'linear-gradient(135deg, #FFE66D 0%, #FFD93D 100%)',
  success: 'linear-gradient(135deg, #27AE60 0%, #229954 100%)',
  error: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
  warning: 'linear-gradient(135deg, #F39C12 0%, #D68910 100%)',
}

// Typography utilities
export const typography = {
  // Display styles
  displayLarge: {
    fontSize: tokens.fonts.body.size.displayLarge,
    lineHeight: tokens.fonts.body.lineHeight.displayLarge,
    fontWeight: tokens.fonts.body.weight.bold,
  },
  displayMedium: {
    fontSize: tokens.fonts.body.size.displayMedium,
    lineHeight: tokens.fonts.body.lineHeight.displayMedium,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  displaySmall: {
    fontSize: tokens.fonts.body.size.displaySmall,
    lineHeight: tokens.fonts.body.lineHeight.displaySmall,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  
  // Headline styles
  headlineLarge: {
    fontSize: tokens.fonts.body.size.headlineLarge,
    lineHeight: tokens.fonts.body.lineHeight.headlineLarge,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  headlineMedium: {
    fontSize: tokens.fonts.body.size.headlineMedium,
    lineHeight: tokens.fonts.body.lineHeight.headlineMedium,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  headlineSmall: {
    fontSize: tokens.fonts.body.size.headlineSmall,
    lineHeight: tokens.fonts.body.lineHeight.headlineSmall,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  
  // Title styles
  titleLarge: {
    fontSize: tokens.fonts.body.size.titleLarge,
    lineHeight: tokens.fonts.body.lineHeight.titleLarge,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  titleMedium: {
    fontSize: tokens.fonts.body.size.titleMedium,
    lineHeight: tokens.fonts.body.lineHeight.titleMedium,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  titleSmall: {
    fontSize: tokens.fonts.body.size.titleSmall,
    lineHeight: tokens.fonts.body.lineHeight.titleSmall,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  
  // Body styles
  bodyLarge: {
    fontSize: tokens.fonts.body.size.bodyLarge,
    lineHeight: tokens.fonts.body.lineHeight.bodyLarge,
    fontWeight: tokens.fonts.body.weight.regular,
  },
  bodyMedium: {
    fontSize: tokens.fonts.body.size.bodyMedium,
    lineHeight: tokens.fonts.body.lineHeight.bodyMedium,
    fontWeight: tokens.fonts.body.weight.regular,
  },
  bodySmall: {
    fontSize: tokens.fonts.body.size.bodySmall,
    lineHeight: tokens.fonts.body.lineHeight.bodySmall,
    fontWeight: tokens.fonts.body.weight.regular,
  },
  
  // Label styles
  labelLarge: {
    fontSize: tokens.fonts.body.size.labelLarge,
    lineHeight: tokens.fonts.body.lineHeight.labelLarge,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  labelMedium: {
    fontSize: tokens.fonts.body.size.labelMedium,
    lineHeight: tokens.fonts.body.lineHeight.labelMedium,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
  labelSmall: {
    fontSize: tokens.fonts.body.size.labelSmall,
    lineHeight: tokens.fonts.body.lineHeight.labelSmall,
    fontWeight: tokens.fonts.body.weight.semibold,
  },
}

// Layout utilities
export const layout = {
  // Container sizes
  container: {
    xs: 320,
    sm: 480,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  },
  
  // Component padding
  padding: {
    buttonHorizontal: 24,
    buttonVertical: 12,
    card: 16,
    screenHorizontal: 16,
    screenVertical: 24,
    input: 16,
  },
  
  // Component margins
  margin: {
    section: 24,
    component: 16,
    element: 8,
  },
  
  // Gap sizes
  gap: {
    small: 8,
    medium: 16,
    large: 24,
  },
}

// Utility functions
export const getSpacing = (value: keyof typeof tokens.space) => tokens.space[value]
export const getSize = (value: keyof typeof tokens.size) => tokens.size[value]
export const getRadius = (value: keyof typeof tokens.radius) => tokens.radius[value]
export const getColor = (value: keyof typeof tokens.color) => tokens.color[value]

// Theme-aware utility functions
export const createThemeAwareStyle = (lightStyle: any, darkStyle: any) => ({
  light: lightStyle,
  dark: darkStyle,
})

// Responsive utilities
export const breakpoints = {
  xs: 0,
  sm: 480,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
}

export const mediaQueries = {
  xs: `@media (min-width: ${breakpoints.xs}px)`,
  sm: `@media (min-width: ${breakpoints.sm}px)`,
  md: `@media (min-width: ${breakpoints.md}px)`,
  lg: `@media (min-width: ${breakpoints.lg}px)`,
  xl: `@media (min-width: ${breakpoints.xl}px)`,
  '2xl': `@media (min-width: ${breakpoints['2xl']}px)`,
}
