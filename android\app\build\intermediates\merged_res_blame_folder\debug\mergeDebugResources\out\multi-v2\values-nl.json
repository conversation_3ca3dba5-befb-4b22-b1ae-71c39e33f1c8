{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,12839", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,12917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4777,5184,5285,5396", "endColumns": "102,100,110,98", "endOffsets": "4875,5280,5391,5490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1436,1529,1591,1657,1715,1788,1852,1919,1988,2045,2101,2223,2280,2342,2398,2474,2608,2693,2772,2870,2956,3042,3180,3261,3340,3464,3554,3631,3688,3739,3805,3883,3966,4037,4113,4188,4267,4340,4411,4520,4614,4692,4781,4871,4945,5026,5115,5168,5247,5314,5395,5479,5541,5605,5668,5739,5847,5959,6061,6172,6274,6373,6434,6489,6570,6653,6729", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,74,92,61,65,57,72,63,66,68,56,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,88,52,78,66,80,83,61,63,62,70,107,111,101,110,101,98,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1431,1524,1586,1652,1710,1783,1847,1914,1983,2040,2096,2218,2275,2337,2393,2469,2603,2688,2767,2865,2951,3037,3175,3256,3335,3459,3549,3626,3683,3734,3800,3878,3961,4032,4108,4183,4262,4335,4406,4515,4609,4687,4776,4866,4940,5021,5110,5163,5242,5309,5390,5474,5536,5600,5663,5734,5842,5954,6056,6167,6269,6368,6429,6484,6565,6648,6724,6796"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3117,3203,3285,3362,3460,4288,4385,4507,4971,5031,5095,6865,7104,7167,7242,7335,7397,7463,7521,7594,7658,7725,7794,7851,7907,8029,8086,8148,8204,8280,8564,8649,8728,8826,8912,8998,9136,9217,9296,9420,9510,9587,9644,9695,9761,9839,9922,9993,10069,10144,10223,10296,10367,10476,10570,10648,10737,10827,10901,10982,11071,11124,11203,11270,11351,11435,11497,11561,11624,11695,11803,11915,12017,12128,12230,12329,12390,12445,12922,13005,13081", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,74,92,61,65,57,72,63,66,68,56,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,88,52,78,66,80,83,61,63,62,70,107,111,101,110,101,98,60,54,80,82,75,71", "endOffsets": "314,3198,3280,3357,3455,3549,4380,4502,4583,5026,5090,5179,6939,7162,7237,7330,7392,7458,7516,7589,7653,7720,7789,7846,7902,8024,8081,8143,8199,8275,8409,8644,8723,8821,8907,8993,9131,9212,9291,9415,9505,9582,9639,9690,9756,9834,9917,9988,10064,10139,10218,10291,10362,10471,10565,10643,10732,10822,10896,10977,11066,11119,11198,11265,11346,11430,11492,11556,11619,11690,11798,11910,12012,12123,12225,12324,12385,12440,12521,13000,13076,13148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,260,381,519,653,782,910,1052,1150,1290,1436", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "164,255,376,514,648,777,905,1047,1145,1285,1431,1557"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4663,4880,5495,5616,5754,5888,6017,6145,6287,6385,6525,6671", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "4772,4966,5611,5749,5883,6012,6140,6282,6380,6520,6666,6792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3554,3656,3758,3858,3958,4065,4169,13783", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3651,3753,3853,3953,4060,4164,4283,13879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,274,348,434,508,584,668,747,819,897,975,1049,1136,1220,1297,1368,1438,1527,1605,1690", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,201,269,343,429,503,579,663,742,814,892,970,1044,1131,1215,1292,1363,1433,1522,1600,1685,1759"}, "to": {"startLines": "33,49,69,71,72,91,92,141,142,143,144,149,150,151,152,153,154,155,156,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3041,4588,6797,6944,7018,8414,8488,12526,12610,12689,12761,13153,13231,13305,13392,13476,13553,13624,13694,13884,13962,14047", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "3112,4658,6860,7013,7099,8483,8559,12605,12684,12756,12834,13226,13300,13387,13471,13548,13619,13689,13778,13957,14042,14116"}}]}]}