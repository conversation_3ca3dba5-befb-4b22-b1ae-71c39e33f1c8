import React from 'react';
import { ActivityIndicator } from 'react-native';
import { View, Text, useTheme } from 'tamagui';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color,
  message,
  overlay = false,
}) => {
  const theme = useTheme();
  const spinnerColor = color || theme.color?.val || '#007AFF';

  const content = (
    <View
      justifyContent="center"
      alignItems="center"
      gap="$md"
      padding="$lg"
    >
      <ActivityIndicator
        size={size}
        color={spinnerColor}
      />
      {message && (
        <Text
          fontSize="$md"
          color="$color"
          textAlign="center"
        >
          {message}
        </Text>
      )}
    </View>
  );

  if (overlay) {
    return (
      <View
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        backgroundColor="rgba(0, 0, 0, 0.5)"
        justifyContent="center"
        alignItems="center"
        zIndex={9999}
      >
        <View
          backgroundColor="$background"
          borderRadius="$md"
          padding="$lg"
          minWidth={120}
        >
          {content}
        </View>
      </View>
    );
  }

  return content;
};
