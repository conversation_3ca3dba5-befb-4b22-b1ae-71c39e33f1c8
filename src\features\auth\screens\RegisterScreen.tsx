import { Text, View } from '@tamagui/core'
import { useRouter } from 'expo-router'
import { useState } from 'react'
import { Alert, KeyboardAvoidingView, Platform, Pressable, ScrollView } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Button } from '../../../../components/ui/Button'
import { AppIcons } from '../../../../components/ui/Icons'
import { Input } from '../../../../components/ui/Input'
import { PasswordInput } from '../../../../components/ui/PasswordInput'
import { ScreenHeader } from '../../../../components/ui/ScreenHeader'
import { useTheme } from '../../../../theme/DaltiThemeProvider'
import { useAuthStore } from '../../../stores/auth.store'
import { useProviderCategories } from '../hooks/useProviderCategories'

export default function RegisterScreen() {
  const router = useRouter()
  const { isDark } = useTheme()

  const insets = useSafeAreaInsets()
  const [fullName, setFullName] = useState('')
  const [email, setEmail] = useState('')
  const [phone, setPhone] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [businessName, setBusinessName] = useState('')
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false)

  // Auth store
  const { register, isLoading, error, clearError } = useAuthStore()

  // Provider categories
  const { parentCategories, isLoading: categoriesLoading, error: categoriesError } = useProviderCategories()

  // Authentication screen color scheme
  const authColors = isDark ? {
    screenBackground: '#257587',
    contentBackground: '#1C2127',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#257587',
    linkColor: '#257587',
    secondaryText: '#B8BCC8',
  } : {
    screenBackground: '#15424E',
    contentBackground: '#FAFAFA',
    headerText: '#FFFFFF',
    headerSubtext: 'rgba(255, 255, 255, 0.9)',
    inputIcons: '#15424E',
    linkColor: '#15424E',
    secondaryText: '#7F8C8D',
  }

  const handleBack = () => {
    router.back()
  }

  const handleRegister = async () => {
    if (!fullName.trim() || !email.trim() || !phone.trim() || !password.trim() || !businessName.trim()) {
      Alert.alert('Error', 'Please fill in all required fields')
      return
    }

    if (!selectedCategoryId) {
      Alert.alert('Error', 'Please select a business category')
      return
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match')
      return
    }

    clearError()

    const registrationData = {
      firstName: fullName.split(' ')[0],
      lastName: fullName.split(' ').slice(1).join(' ') || '',
      email: email.trim(),
      phone: phone.trim(),
      password,
      businessName: businessName.trim(),
      providerCategoryId: selectedCategoryId,
    }

    try {
      const success = await register(registrationData)

      if (success) {
        Alert.alert('Success', 'OTP sent to your email!', [
          {
            text: 'OK',
            onPress: () => router.push({
              pathname: '/otp-verification',
              params: registrationData,
            }),
          },
        ])
      } else {
        Alert.alert('Registration Failed', error || 'Failed to send OTP')
      }
    } catch {
      Alert.alert('Error', 'An unexpected error occurred')
    }
  }

  const handleSignIn = () => {
    router.push('/login')
  }

  return (
    <View
      flex={1}
      style={{
        backgroundColor: authColors.screenBackground,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <ScreenHeader
        showBackButton
        onBackPress={handleBack}
        showLanguageToggle={false}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
          {/* Header section */}
          <View paddingHorizontal="$lg" paddingBottom="$xl">
            <Text
              fontSize="$headlineMedium"
              fontWeight="bold"
              color={authColors.headerText}
              marginBottom="$sm"
            >
              Welcome to Dalti Provider
            </Text>
            <Text
              fontSize="$bodyLarge"
              color={authColors.headerSubtext}
            >
              Create your account to get started
            </Text>
          </View>

          {/* Form section */}
          <View
            flex={1}
            backgroundColor={authColors.contentBackground}
            borderTopLeftRadius="$2xl"
            borderTopRightRadius="$2xl"
            paddingHorizontal="$lg"
            paddingTop="$2xl"
            paddingBottom="$xl"
          >
            <View gap="$lg">
              {/* Full Name Input */}
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                value={fullName}
                onChangeText={setFullName}
                variant="outlined"
                leftIcon={<AppIcons.person size={20} color={authColors.inputIcons} />}
              />

              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                variant="outlined"
                leftIcon={<AppIcons.mail size={20} color={authColors.inputIcons} />}
              />

              {/* Phone Input */}
              <Input
                label="Phone Number"
                placeholder="Enter your phone number"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                variant="outlined"
                leftIcon={<AppIcons.call size={20} color={authColors.inputIcons} />}
              />

              {/* Business Name Input */}
              <Input
                label="Business Name"
                placeholder="Enter your business name"
                value={businessName}
                onChangeText={setBusinessName}
                variant="outlined"
                leftIcon={<AppIcons.business size={20} color={authColors.inputIcons} />}
              />

              {/* Provider Category Selection */}
              <View>
                <Text
                  fontSize="$bodyMedium"
                  color={authColors.inputLabels}
                  marginBottom="$sm"
                  fontWeight="semibold"
                >
                  Business Category
                </Text>
                <Pressable onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}>
                  <View
                    backgroundColor={authColors.inputContainerBg}
                    borderRadius="$md"
                    borderWidth={1}
                    borderColor={authColors.inputIcons}
                    paddingHorizontal="$md"
                    paddingVertical="$md"
                    flexDirection="row"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <View flexDirection="row" alignItems="center" flex={1}>
                      <AppIcons.category size={20} color={authColors.inputIcons} />
                      <Text
                        marginLeft="$sm"
                        color={selectedCategoryId ? authColors.inputText : authColors.secondaryText}
                        fontSize="$bodyMedium"
                      >
                        {selectedCategoryId
                          ? parentCategories.find(cat => cat.id === selectedCategoryId)?.title || 'Select a category'
                          : 'Select a category'
                        }
                      </Text>
                    </View>
                    <AppIcons.chevronDown size={20} color={authColors.inputIcons} />
                  </View>
                </Pressable>
              </View>

              {/* Password Input */}
              <PasswordInput
                label="Password"
                placeholder="Create a password"
                value={password}
                onChangeText={setPassword}
                showStrengthIndicator
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
              />

              {/* Confirm Password Input */}
              <PasswordInput
                label="Confirm Password"
                placeholder="Confirm your password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                variant="outlined"
                leftIcon={<AppIcons.lock size={20} color={authColors.inputIcons} />}
              />

              {/* Sign Up Button */}
              <Button
                title="Create Account"
                variant="primary"
                size="lg"
                fullWidth
                loading={isLoading}
                onPress={handleRegister}
                containerStyle={{ marginTop: 16 }}
              />

              {/* Sign In Link */}
              <View 
                flexDirection="row" 
                alignItems="center" 
                justifyContent="center" 
                gap="$sm"
                marginTop="$lg"
              >
                <Text
                  fontSize="$bodyMedium"
                  color={authColors.secondaryText}
                >
                  Already have an account?
                </Text>
                <Pressable onPress={handleSignIn}>
                  <Text
                    fontSize="$bodyMedium"
                    color={authColors.linkColor}
                    fontWeight="semibold"
                  >
                    Sign In
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Category Selection Dropdown */}
      {showCategoryDropdown && (
        <View
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          backgroundColor="rgba(0,0,0,0.5)"
          zIndex={1000}
          justifyContent="center"
          alignItems="center"
        >
          <View
            backgroundColor={authColors.contentBackground}
            borderRadius="$lg"
            paddingHorizontal="$lg"
            paddingVertical="$lg"
            marginHorizontal="$lg"
            maxHeight="60%"
            width="90%"
          >
            <View flexDirection="row" justifyContent="space-between" alignItems="center" marginBottom="$lg">
              <Text fontSize="$headlineSmall" fontWeight="bold" color={authColors.inputText}>
                Select Business Category
              </Text>
              <Pressable onPress={() => setShowCategoryDropdown(false)}>
                <AppIcons.close size={24} color={authColors.inputIcons} />
              </Pressable>
            </View>

            {categoriesLoading ? (
              <Text padding="$lg" textAlign="center" color={authColors.secondaryText}>
                Loading categories...
              </Text>
            ) : categoriesError ? (
              <Text padding="$lg" textAlign="center" color="red">
                Failed to load categories
              </Text>
            ) : (
              <ScrollView showsVerticalScrollIndicator={false}>
                {parentCategories.map((item) => (
                  <Pressable
                    key={item.id}
                    onPress={() => {
                      setSelectedCategoryId(item.id)
                      setShowCategoryDropdown(false)
                    }}
                  >
                    <View
                      paddingVertical="$md"
                      paddingHorizontal="$sm"
                      borderBottomWidth={1}
                      borderBottomColor={authColors.inputContainerBg}
                      flexDirection="row"
                      alignItems="center"
                      justifyContent="space-between"
                    >
                      <Text fontSize="$bodyLarge" color={authColors.inputText}>
                        {item.title}
                      </Text>
                      {selectedCategoryId === item.id && (
                        <AppIcons.checkmark size={20} color={authColors.linkColor} />
                      )}
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            )}
          </View>
        </View>
      )}
    </View>
  )
}
