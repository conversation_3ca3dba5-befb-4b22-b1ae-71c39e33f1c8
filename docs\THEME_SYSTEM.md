# Dalti Provider Theme System

## Overview

The Dalti Provider app uses a comprehensive theme system built on top of **Tamagui UI** that provides:

- ✅ Light and Dark mode support
- ✅ RTL (Right-to-Left) layout support
- ✅ Custom UI components with consistent styling
- ✅ System theme detection and automatic switching
- ✅ Theme persistence across app sessions
- ✅ Responsive design tokens
- ✅ Comprehensive color palette
- ✅ Typography system with Inter font family

## Architecture

### Core Components

1. **ThemeProvider** (`src/components/providers/ThemeProvider.tsx`)
   - Manages global theme state
   - Handles system theme detection
   - Provides RTL support
   - Persists user preferences

2. **Tamagui Configuration** (`tamagui.config.ts`)
   - Defines design tokens (colors, spacing, typography)
   - Sets up theme variants
   - Configures animations and media queries

3. **Custom UI Components** (`src/components/ui/`)
   - Built on top of Tamagui components
   - Consistent styling and behavior
   - RTL-aware layouts

4. **Theme Utilities** (`src/utils/theme.ts` & `src/hooks/useTheme.ts`)
   - Helper functions for theme management
   - Custom hooks for accessing theme state
   - RTL style conversion utilities

## Usage

### Basic Setup

The theme system is automatically initialized in the root layout:

```tsx
import { ThemeProvider } from '../src/components/providers/ThemeProvider'

export default function RootLayout() {
  return (
    <ThemeProvider
      config={{
        defaultMode: 'system',
        defaultDirection: 'ltr',
        persistPreferences: true,
        systemThemeEnabled: true,
      }}
    >
      {/* Your app content */}
    </ThemeProvider>
  )
}
```

### Using Theme Hooks

```tsx
import { useTheme, useThemeColors, useThemeMode } from '../src/hooks/useTheme'

function MyComponent() {
  // Access full theme context
  const { isDark, isRTL, colors } = useTheme()
  
  // Access only colors
  const colors = useThemeColors()
  
  // Access only theme mode utilities
  const { mode, toggleMode } = useThemeMode()
  
  return (
    <View style={{ backgroundColor: colors.background }}>
      <Button onPress={toggleMode}>
        Switch to {isDark ? 'Light' : 'Dark'} Mode
      </Button>
    </View>
  )
}
```

### Using Custom UI Components

```tsx
import { Button, Text, View } from '../src/components/ui'

function MyScreen() {
  return (
    <View variant="card" padding="lg">
      <Text variant="heading" size="xl">
        Welcome to Dalti Provider
      </Text>
      
      <Button variant="primary" size="lg" fullWidth>
        Get Started
      </Button>
    </View>
  )
}
```

## Theme Configuration

### Color Palette

The theme system includes a comprehensive color palette:

- **Brand Colors**: Primary, Secondary, Accent
- **Status Colors**: Success, Warning, Error, Info
- **Neutral Colors**: Background, Surface, Text, Border
- **Interactive States**: Hover, Press, Focus, Disabled

### Typography

Uses the Inter font family with multiple weights:
- Light (300)
- Regular (400)
- Medium (500)
- SemiBold (600)
- Bold (700)
- ExtraBold (800)
- Black (900)

### Spacing System

Consistent spacing tokens from 0.5 to 96 units:
```tsx
// Usage examples
<View padding="md" margin="lg" />
<Button size="sm" />
<Text size="xl" />
```

### Component Variants

Each UI component supports multiple variants:

**Button Variants:**
- `primary`, `secondary`, `accent`
- `success`, `warning`, `error`
- `outline`, `ghost`, `link`

**Text Variants:**
- `heading`, `subheading`, `body`, `caption`, `overline`

**View Variants:**
- `default`, `card`, `surface`, `overlay`

## RTL Support

The theme system provides comprehensive RTL support:

### Automatic Layout Direction

```tsx
const { isRTL, toggleDirection } = useLayoutDirection()

// Automatically adjusts text alignment
<Text align="left">This text respects RTL</Text>

// Use RTL-aware style utilities
const style = createRTLStyle({
  marginStart: 16,
  paddingEnd: 8,
}, direction)
```

### RTL-Aware Components

All custom UI components automatically handle RTL layouts:
- Text alignment
- Icon positioning
- Margin/padding direction
- Border positioning

## Dark Mode

### System Theme Detection

The theme system automatically detects and responds to system theme changes:

```tsx
const { systemTheme, mode } = useTheme()

// mode can be 'light', 'dark', or 'system'
// systemTheme is always 'light' or 'dark'
```

### Manual Theme Control

```tsx
const { setMode, toggleMode } = useThemeMode()

// Set specific mode
setMode('dark')

// Toggle between light and dark
toggleMode()
```

## Customization

### Adding Custom Colors

Update `src/types/theme.ts` and `tamagui.config.ts`:

```tsx
// In tamagui.config.ts
const customTokens = createTokens({
  color: {
    // Add your custom colors
    brand: '#FF6B6B',
    brandLight: '#FF8E8E',
    brandDark: '#FF4848',
  }
})
```

### Creating Custom Components

```tsx
import { styled } from '@tamagui/core'
import { Button as TamaguiButton } from '@tamagui/core'

const CustomButton = styled(TamaguiButton, {
  name: 'CustomButton',
  variants: {
    variant: {
      custom: {
        backgroundColor: '$brand',
        color: '$white',
      }
    }
  }
})
```

## Best Practices

1. **Always use theme tokens** instead of hardcoded values
2. **Test both light and dark modes** during development
3. **Verify RTL layouts** for international users
4. **Use semantic color names** (e.g., `primary` instead of `blue`)
5. **Leverage component variants** for consistent styling
6. **Persist user preferences** for better UX

## Testing

The theme system includes a comprehensive test screen at `app/index.tsx` that demonstrates:
- Theme switching functionality
- RTL layout support
- All component variants
- Color palette display
- Typography samples

Run the app and test the theme controls to verify everything works correctly.

## Dependencies

- `@tamagui/core` - Core styling engine
- `@tamagui/config` - Default configuration
- `@tamagui/animations-react-native` - Animation support
- `@tamagui/font-inter` - Inter font integration
- `@tamagui/colors` - Color palette
- `@expo-google-fonts/inter` - Inter font files
- `expo-secure-store` - Theme preference persistence
