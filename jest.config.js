module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js|jsx)',
    '**/*.(test|spec).(ts|tsx|js|jsx)',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
    'app/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/index.{ts,tsx}',
    '!**/node_modules/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^\\.\\./(.*)/src/(.*)$': '<rootDir>/src/$2',
    '^\\.\\./(.*)/components/(.*)$': '<rootDir>/components/$2',
    '^\\.\\./(.*)/theme/(.*)$': '<rootDir>/theme/$2',
    '^\\.\\./src/(.*)$': '<rootDir>/src/$1',
    '^\\.\\.\\./src/(.*)$': '<rootDir>/src/$1',
    '^\\.\\./\\.\\./src/(.*)$': '<rootDir>/src/$1',
    '^\\.\\./\\.\\./\\.\\./src/(.*)$': '<rootDir>/src/$1',
    '^\\.\\./\\.\\./\\.\\./\\.\\./src/(.*)$': '<rootDir>/src/$1',
    '^\\.\\./components/(.*)$': '<rootDir>/components/$1',
    '^\\.\\.\\./components/(.*)$': '<rootDir>/components/$1',
    '^\\.\\./\\.\\./components/(.*)$': '<rootDir>/components/$1',
    '^\\.\\./\\.\\./\\.\\./components/(.*)$': '<rootDir>/components/$1',
    '^\\.\\./\\.\\./\\.\\./\\.\\./components/(.*)$': '<rootDir>/components/$1',
    '^\\.\\./theme/(.*)$': '<rootDir>/theme/$1',
    '^\\.\\.\\./theme/(.*)$': '<rootDir>/theme/$1',
    '^\\.\\./\\.\\./theme/(.*)$': '<rootDir>/theme/$1',
    '^\\.\\./\\.\\./\\.\\./theme/(.*)$': '<rootDir>/theme/$1',
    '^\\.\\./\\.\\./\\.\\./\\.\\./theme/(.*)$': '<rootDir>/theme/$1',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|react-native-.*|@react-navigation|@expo|expo|@tamagui|tamagui|react-native-mmkv|react-native-keychain|@react-native-async-storage)/)',
  ],
  testTimeout: 10000,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
};
