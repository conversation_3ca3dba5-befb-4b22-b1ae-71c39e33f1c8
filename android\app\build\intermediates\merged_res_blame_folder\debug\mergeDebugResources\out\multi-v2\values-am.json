{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,11149", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,11224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1311,1396,1459,1521,1579,1645,1707,1771,1838,1892,1947,2043,2100,2159,2215,2282,2387,2467,2548,2640,2725,2806,2935,3008,3079,3193,3275,3351,3402,3453,3519,3585,3658,3729,3804,3872,3945,4016,4083,4181,4266,4333,4420,4508,4582,4650,4735,4786,4864,4928,5008,5090,5152,5216,5279,5345,5440,5535,5620,5711,5798,5881,5936,5991,6067,6146,6221", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,76,84,62,61,57,65,61,63,66,53,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,86,82,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1306,1391,1454,1516,1574,1640,1702,1766,1833,1887,1942,2038,2095,2154,2210,2277,2382,2462,2543,2635,2720,2801,2930,3003,3074,3188,3270,3346,3397,3448,3514,3580,3653,3724,3799,3867,3940,4011,4078,4176,4261,4328,4415,4503,4577,4645,4730,4781,4859,4923,5003,5085,5147,5211,5274,5340,5435,5530,5615,5706,5793,5876,5931,5986,6062,6141,6216,6287"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3225,3993,4082,4184,4550,4609,4673,6196,6258,6316,6393,6478,6541,6603,6661,6727,6789,6853,6920,6974,7029,7125,7182,7241,7297,7364,7469,7549,7630,7722,7807,7888,8017,8090,8161,8275,8357,8433,8484,8535,8601,8667,8740,8811,8886,8954,9027,9098,9165,9263,9348,9415,9502,9590,9664,9732,9817,9868,9946,10010,10090,10172,10234,10298,10361,10427,10522,10617,10702,10793,10880,10963,11018,11073,11229,11308,11383", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,76,84,62,61,57,65,61,63,66,53,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,86,82,54,54,75,78,74,70", "endOffsets": "306,2995,3063,3138,3220,3301,4077,4179,4256,4604,4668,4753,6253,6311,6388,6473,6536,6598,6656,6722,6784,6848,6915,6969,7024,7120,7177,7236,7292,7359,7464,7544,7625,7717,7802,7883,8012,8085,8156,8270,8352,8428,8479,8530,8596,8662,8735,8806,8881,8949,9022,9093,9160,9258,9343,9410,9497,9585,9659,9727,9812,9863,9941,10005,10085,10167,10229,10293,10356,10422,10517,10612,10697,10788,10875,10958,11013,11068,11144,11303,11378,11449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,248,355,467,583,703,819,934,1026,1158,1283", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "156,243,350,462,578,698,814,929,1021,1153,1278,1383"}, "to": {"startLines": "48,50,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4261,4463,5056,5163,5275,5391,5511,5627,5742,5834,5966,6091", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "4362,4545,5158,5270,5386,5506,5622,5737,5829,5961,6086,6191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "49,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4367,4758,4853,4959", "endColumns": "95,94,105,96", "endOffsets": "4458,4848,4954,5051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3306,3399,3499,3596,3695,3791,3893,11454", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3394,3494,3591,3690,3786,3888,3988,11550"}}]}]}