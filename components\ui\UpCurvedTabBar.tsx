import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, { Path } from 'react-native-svg';
import { useTheme } from '../../theme/DaltiThemeProvider';
import { AppIcons } from './Icons';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ name, focused, color, size }) => {
  switch (name) {
    case 'index':
      return <AppIcons.grid size={size} color={color} />;
    case 'calendar':
      return <AppIcons.calendar size={size} color={color} />;
    case 'clients':
      return <AppIcons.people size={size} color={color} />;
    case 'bookings':
      return <AppIcons.bookmarks size={size} color={color} />;
    default:
      return <AppIcons.home size={size} color={color} />;
  }
};

export const UpCurvedTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const insets = useSafeAreaInsets();
  const { isDark } = useTheme();

  // Debug log to confirm component is loading
  console.log('🎨 UpCurvedTabBar loaded - insets.bottom:', insets.bottom);

  // Theme colors
  const tabBarBg = isDark ? '#1C2127' : '#FFFFFF';
  const borderColor = isDark ? '#3A4048' : '#E1E8ED';
  const activeColor = isDark ? '#3B82F6' : '#3B82F6';
  const inactiveColor = isDark ? '#9CA3AF' : '#6B7280';
  const centerButtonBg = isDark ? '#3B82F6' : '#2563EB'; // Slightly different shades for light/dark

  const handleQRPress = () => {
    console.log('🔥 UP CURVED TAB BAR - QR Scanner pressed');
    // Add your QR scanner logic here
  };

  const tabBarHeight = 70;
  const centerButtonSize = 56;
  const curveHeight = 25; // Start curve higher up
  const curveDepth = 35; // How deep the curve goes up
  const buttonOffset = 15; // How much button overlaps the bar

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* SVG Upward Curved Background */}
      <Svg
        width={screenWidth}
        height={tabBarHeight}
        style={styles.svgBackground}
      >
        <Path
          d={`M0,${curveHeight + curveDepth}
             L${screenWidth * 0.25},${curveHeight + curveDepth}
             Q${screenWidth * 0.35},${curveHeight + curveDepth} ${screenWidth * 0.4},${curveHeight + 25}
             Q${screenWidth * 0.45},${curveHeight + 10} ${screenWidth * 0.5},${curveHeight}
             Q${screenWidth * 0.55},${curveHeight + 10} ${screenWidth * 0.6},${curveHeight + 25}
             Q${screenWidth * 0.65},${curveHeight + curveDepth} ${screenWidth * 0.75},${curveHeight + curveDepth}
             L${screenWidth},${curveHeight + curveDepth}
             L${screenWidth},${tabBarHeight}
             L0,${tabBarHeight}
             Z`}
          fill={tabBarBg}
          stroke={borderColor}
          strokeWidth={1}
        />
      </Svg>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {/* Left Tabs */}
        <View style={styles.leftTabs}>
          {state.routes.slice(0, 2).map((route, index) => {
            const { options } = descriptors[route.key];
            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <TouchableOpacity
                key={route.key}
                onPress={onPress}
                style={styles.tabButton}
              >
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={24}
                />
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Background circle for better integration */}
        <View
          style={{
            position: 'absolute',
            bottom: curveHeight + curveDepth - 4, // Position on the UP curve
            left: (screenWidth - centerButtonSize - 8) / 2,
            width: centerButtonSize + 8,
            height: centerButtonSize + 8,
            borderRadius: (centerButtonSize + 8) / 2,
            backgroundColor: tabBarBg,
            borderWidth: 2,
            borderColor: borderColor,
            zIndex: 9,
          }}
        />

        {/* Center QR Button - Positioned on top of the UP curve */}
        <TouchableOpacity
          style={[
            styles.centerButton,
            {
              backgroundColor: centerButtonBg,
              width: centerButtonSize,
              height: centerButtonSize,
              borderRadius: centerButtonSize / 2,
              bottom: curveHeight + curveDepth, // Position on the curve peak
              position: 'absolute',
              left: (screenWidth - centerButtonSize) / 2, // Center horizontally
              zIndex: 10, // Ensure it's on top
            }
          ]}
          onPress={handleQRPress}
        >
          <AppIcons.qrCode size={26} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Right Tabs */}
        <View style={styles.rightTabs}>
          {state.routes.slice(2, 4).map((route, index) => {
            const actualIndex = index + 2;
            const { options } = descriptors[route.key];
            const isFocused = state.index === actualIndex;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <TouchableOpacity
                key={route.key}
                onPress={onPress}
                style={styles.tabButton}
              >
                <TabBarIcon
                  name={route.name}
                  focused={isFocused}
                  color={isFocused ? activeColor : inactiveColor}
                  size={24}
                />
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  svgBackground: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 80,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  leftTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  rightTabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  centerButton: {
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    // Add a subtle border to better integrate with the design
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
});
