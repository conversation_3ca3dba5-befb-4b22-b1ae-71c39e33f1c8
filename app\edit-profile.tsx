import { useRouter } from 'expo-router'
import React, { useState } from 'react'
import { Pressable, TextInput } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { ScrollView, Text, View } from 'tamagui'
import { AppBar } from '../components/ui/AppBar'
import { AppIcons } from '../components/ui/Icons'
import { useTheme } from '../theme/DaltiThemeProvider'

// Image Upload Component
interface ImageUploadProps {
  title: string
  subtitle: string
  icon: React.ReactNode
  onPress?: () => void
  hasImage?: boolean
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  title,
  subtitle,
  icon,
  onPress,
  hasImage = false
}) => {
  const { isDark } = useTheme()
  
  const containerBg = isDark ? "#2A3038" : "#F5F5F5"
  const titleColor = isDark ? "#F8F9FA" : "#2C3E50"
  const subtitleColor = isDark ? "#B8BCC8" : "#7F8C8D"

  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => ({
        backgroundColor: containerBg,
        borderRadius: 8,
        padding: 16,
        alignItems: 'center',
        flex: 1,
        opacity: pressed ? 0.7 : 1,
        position: 'relative',
      })}
    >
      <View
        width={60}
        height={60}
        borderRadius={8}
        backgroundColor={isDark ? "#1C2127" : "#E5E7EB"}
        alignItems="center"
        justifyContent="center"
        marginBottom="$sm"
      >
        {icon}
      </View>
      
      {hasImage && (
        <View
          position="absolute"
          bottom={45}
          right={45}
          width={24}
          height={24}
          borderRadius={12}
          backgroundColor="#257587"
          alignItems="center"
          justifyContent="center"
        >
          <AppIcons.camera size={12} color="#FFFFFF" />
        </View>
      )}
      
      <Text
        fontSize="$bodyMedium"
        fontWeight="500"
        color={titleColor}
        textAlign="center"
        marginBottom="$xs"
      >
        {title}
      </Text>
      <Text
        fontSize="$bodySmall"
        color={subtitleColor}
        textAlign="center"
      >
        {subtitle}
      </Text>
    </Pressable>
  )
}

// Input Field Component
interface InputFieldProps {
  icon: React.ReactNode
  label: string
  value: string
  onChangeText: (text: string) => void
  placeholder?: string
  editable?: boolean
  multiline?: boolean
  showClearButton?: boolean
  onClear?: () => void
}

const InputField: React.FC<InputFieldProps> = ({
  icon,
  label,
  value,
  onChangeText,
  placeholder,
  editable = true,
  multiline = false,
  showClearButton = false,
  onClear
}) => {
  const { isDark } = useTheme()
  
  const labelColor = isDark ? "#257587" : "#257587"
  const inputBg = isDark ? "#1C2127" : "#FFFFFF"
  const inputTextColor = isDark ? "#F8F9FA" : "#2C3E50"
  const placeholderColor = isDark ? "#B8BCC8" : "#7F8C8D"
  const disabledBg = isDark ? "#2A3038" : "#F5F5F5"

  return (
    <View marginBottom="$lg">
      <View flexDirection="row" alignItems="center" gap="$sm" marginBottom="$sm">
        {icon}
        <Text
          fontSize="$bodyLarge"
          fontWeight="500"
          color={labelColor}
        >
          {label}
        </Text>
      </View>
      
      <View
        backgroundColor={editable ? inputBg : disabledBg}
        borderRadius={8}
        padding="$md"
        flexDirection="row"
        alignItems={multiline ? "flex-start" : "center"}
        minHeight={multiline ? 100 : 50}
      >
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={placeholderColor}
          editable={editable}
          multiline={multiline}
          style={{
            flex: 1,
            fontSize: 16,
            color: inputTextColor,
            textAlignVertical: multiline ? 'top' : 'center',
            paddingTop: multiline ? 8 : 0,
          }}
        />
        
        {showClearButton && value.length > 0 && editable && (
          <Pressable onPress={onClear} style={{ marginLeft: 8 }}>
            <AppIcons.close size={20} color={placeholderColor} />
          </Pressable>
        )}
        
        {!editable && (
          <AppIcons.lock size={20} color={placeholderColor} />
        )}
      </View>
    </View>
  )
}

// Section Header Component
interface SectionHeaderProps {
  title: string
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ title }) => {
  const { isDark } = useTheme()
  const sectionTitleColor = isDark ? "#257587" : "#257587"

  return (
    <Text
      fontSize="$titleMedium"
      fontWeight="600"
      color={sectionTitleColor}
      marginBottom="$md"
      marginTop="$lg"
    >
      {title}
    </Text>
  )
}

export default function EditProfileScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { isDark } = useTheme()

  // Form state
  const [professionalTitle, setProfessionalTitle] = useState('clinique du testeur 2')
  const [phoneNumber, setPhoneNumber] = useState('0558880153')
  const [aboutMe, setAboutMe] = useState('une description du service')

  const screenBg = isDark ? '#0F1419' : '#FAFAFA'

  const handleBack = () => {
    router.back()
  }

  const handleSave = () => {
    console.log('Save profile changes')
    // TODO: Implement save logic
    router.back()
  }

  const handleProfilePictureUpload = () => {
    console.log('Upload profile picture')
    // TODO: Implement image picker
  }

  const handleBusinessLogoUpload = () => {
    console.log('Upload business logo')
    // TODO: Implement image picker
  }

  return (
    <View flex={1} backgroundColor={screenBg} style={{ paddingTop: insets.top }}>
      <AppBar
        title="Edit Profile"
        startIcon={{
          icon: <AppIcons.back size={20} />,
          onPress: handleBack,
        }}
        endIcon={{
          icon: <AppIcons.checkmark size={20} />,
          onPress: handleSave,
        }}
      />

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View padding="$lg" gap="$sm">
          {/* Profile & Business Images Section */}
          <SectionHeader title="Profile & Business Images" />
          
          <View flexDirection="row" gap="$md" marginBottom="$md">
            <ImageUpload
              title="Profile Picture"
              subtitle="Tap to change"
              icon={<AppIcons.person size={32} color={isDark ? "#B8BCC8" : "#7F8C8D"} />}
              onPress={handleProfilePictureUpload}
              hasImage={false}
            />
            
            <ImageUpload
              title="Business Logo"
              subtitle="Tap to upload"
              icon={<AppIcons.business size={32} color={isDark ? "#B8BCC8" : "#7F8C8D"} />}
              onPress={handleBusinessLogoUpload}
              hasImage={false}
            />
          </View>

          {/* Basic Information Section */}
          <SectionHeader title="Basic Information" />
          
          <InputField
            icon={<AppIcons.business size={20} color="#257587" />}
            label="Professional Title"
            value={professionalTitle}
            onChangeText={setProfessionalTitle}
            showClearButton={true}
            onClear={() => setProfessionalTitle('')}
          />
          
          <InputField
            icon={<AppIcons.call size={20} color="#257587" />}
            label="Phone Number"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            showClearButton={true}
            onClear={() => setPhoneNumber('')}
          />
          
          <InputField
            icon={<AppIcons.people size={20} color="#257587" />}
            label="Category"
            value="Doctor"
            onChangeText={() => {}}
            editable={false}
          />
          
          <InputField
            icon={<AppIcons.helpCircle size={20} color="#257587" />}
            label="About Me"
            value={aboutMe}
            onChangeText={setAboutMe}
            multiline={true}
            placeholder="Describe your services..."
          />

          {/* Bottom spacing */}
          <View height={40} />
        </View>
      </ScrollView>
    </View>
  )
}
