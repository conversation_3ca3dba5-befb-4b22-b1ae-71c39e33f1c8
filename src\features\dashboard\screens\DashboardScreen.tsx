import React from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import { Text, View } from 'tamagui';
import { AppIcons } from '../../../../components/ui/Icons';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { useAuthStore } from '../../../stores/auth.store';
import { useDashboardOverview, useDashboardRefresh, useQuickStats, useTodaySchedule } from '../hooks/useDashboard';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color = '#007AFF' }) => {

  
  return (
    <View
      backgroundColor="$background"
      borderRadius="$md"
      padding="$lg"
      shadowColor="$shadowColor"
      shadowOffset={{ width: 0, height: 2 }}
      shadowOpacity={0.1}
      shadowRadius={4}
      elevation={3}
      flex={1}
      minHeight={100}
    >
      <View flexDirection="row" alignItems="center" justifyContent="space-between" marginBottom="$sm">
        <View
          width={40}
          height={40}
          backgroundColor={`${color}20`}
          borderRadius="$sm"
          justifyContent="center"
          alignItems="center"
        >
          {icon}
        </View>
      </View>
      
      <Text fontSize="$headlineSmall" fontWeight="bold" color="$color" marginBottom="$xs">
        {value}
      </Text>
      
      <Text fontSize="$bodySmall" color="$colorSubtle">
        {title}
      </Text>
    </View>
  );
};

interface QueueStatusProps {
  queueName: string;
  currentCount: number;
  maxCapacity: number;
  estimatedWaitTime: number;
}

const QueueStatus: React.FC<QueueStatusProps> = ({ 
  queueName, 
  currentCount, 
  maxCapacity, 
  estimatedWaitTime 
}) => {
  const fillPercentage = (currentCount / maxCapacity) * 100;
  const statusColor = fillPercentage > 80 ? '#FF3B30' : fillPercentage > 60 ? '#FF9500' : '#34C759';
  
  return (
    <View
      backgroundColor="$background"
      borderRadius="$md"
      padding="$lg"
      marginBottom="$md"
      shadowColor="$shadowColor"
      shadowOffset={{ width: 0, height: 1 }}
      shadowOpacity={0.1}
      shadowRadius={2}
      elevation={2}
    >
      <View flexDirection="row" justifyContent="space-between" alignItems="center" marginBottom="$sm">
        <Text fontSize="$bodyLarge" fontWeight="semibold" color="$color">
          {queueName}
        </Text>
        <Text fontSize="$bodyMedium" color="$colorSubtle">
          {currentCount}/{maxCapacity}
        </Text>
      </View>
      
      <View
        height={6}
        backgroundColor="$backgroundSubtle"
        borderRadius={3}
        marginBottom="$sm"
      >
        <View
          height={6}
          backgroundColor={statusColor}
          borderRadius={3}
          width={`${fillPercentage}%`}
        />
      </View>
      
      <Text fontSize="$bodySmall" color="$colorSubtle">
        Est. wait time: {estimatedWaitTime} min
      </Text>
    </View>
  );
};

export const DashboardScreen: React.FC = () => {
  const { user, provider } = useAuthStore();
  const { refreshAll } = useDashboardRefresh();
  
  const {
    data: quickStats,
    isLoading: statsLoading,
    error: statsError,
  } = useQuickStats();
  
  const {
    data: todaySchedule,
    isLoading: scheduleLoading,
    error: scheduleError,
  } = useTodaySchedule();
  
  const {
    data: overview,
    isLoading: overviewLoading,
    error: overviewError,
  } = useDashboardOverview();

  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await refreshAll();
    setRefreshing(false);
  }, [refreshAll]);

  const isLoading = statsLoading || scheduleLoading || overviewLoading;
  const hasError = statsError || scheduleError || overviewError;

  if (isLoading && !quickStats && !todaySchedule && !overview) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <LoadingSpinner size="large" message="Loading dashboard..." />
      </View>
    );
  }

  return (
    <ScrollView
      flex={1}
      backgroundColor="$backgroundSubtle"
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View padding="$lg" gap="$lg">
        {/* Welcome Header */}
        <View>
          <Text fontSize="$headlineLarge" fontWeight="bold" color="$color">
            Welcome back, {user?.firstName || 'Provider'}!
          </Text>
          <Text fontSize="$bodyLarge" color="$colorSubtle" marginTop="$xs">
            {provider?.title || 'Dalti Provider'}
          </Text>
        </View>

        {/* Quick Stats Grid */}
        {quickStats && (
          <View>
            <Text fontSize="$headlineMedium" fontWeight="semibold" color="$color" marginBottom="$md">
              Today&apos;s Overview
            </Text>
            
            <View flexDirection="row" gap="$md" marginBottom="$md">
              <StatCard
                title="Revenue"
                value={`$${quickStats.todayRevenue || 0}`}
                icon={<AppIcons.trending size={24} color="#34C759" />}
                color="#34C759"
              />
              <StatCard
                title="Appointments"
                value={quickStats.todayAppointments || 0}
                icon={<AppIcons.calendar size={24} color="#007AFF" />}
                color="#007AFF"
              />
            </View>
            
            <View flexDirection="row" gap="$md">
              <StatCard
                title="Waiting Customers"
                value={quickStats.waitingCustomers || 0}
                icon={<AppIcons.people size={24} color="#FF9500" />}
                color="#FF9500"
              />
              <StatCard
                title="Active Queues"
                value={quickStats.activeQueues || 0}
                icon={<AppIcons.list size={24} color="#5856D6" />}
                color="#5856D6"
              />
            </View>
          </View>
        )}

        {/* Next Appointment */}
        {todaySchedule?.nextAppointment && (
          <View>
            <Text fontSize="$headlineMedium" fontWeight="semibold" color="$color" marginBottom="$md">
              Next Appointment
            </Text>
            
            <View
              backgroundColor="$background"
              borderRadius="$md"
              padding="$lg"
              shadowColor="$shadowColor"
              shadowOffset={{ width: 0, height: 2 }}
              shadowOpacity={0.1}
              shadowRadius={4}
              elevation={3}
            >
              <View flexDirection="row" alignItems="center" gap="$md">
                <View
                  width={50}
                  height={50}
                  backgroundColor="$backgroundSubtle"
                  borderRadius="$md"
                  justifyContent="center"
                  alignItems="center"
                >
                  <AppIcons.person size={24} color="$color" />
                </View>
                
                <View flex={1}>
                  <Text fontSize="$bodyLarge" fontWeight="semibold" color="$color">
                    {todaySchedule.nextAppointment.customerName}
                  </Text>
                  <Text fontSize="$bodyMedium" color="$colorSubtle">
                    {todaySchedule.nextAppointment.serviceName}
                  </Text>
                  <Text fontSize="$bodySmall" color="$colorSubtle">
                    {new Date(todaySchedule.nextAppointment.startTime).toLocaleTimeString()} • {todaySchedule.nextAppointment.location}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Queue Status */}
        {todaySchedule?.queueStatuses && todaySchedule.queueStatuses.length > 0 && (
          <View>
            <Text fontSize="$headlineMedium" fontWeight="semibold" color="$color" marginBottom="$md">
              Queue Status
            </Text>
            
            {todaySchedule.queueStatuses.map((queue) => (
              <QueueStatus
                key={queue.queueId}
                queueName={queue.queueName}
                currentCount={queue.currentCount}
                maxCapacity={queue.maxCapacity}
                estimatedWaitTime={queue.estimatedWaitTime}
              />
            ))}
          </View>
        )}

        {/* Business Hours */}
        {todaySchedule?.todayHours && (
          <View>
            <Text fontSize="$headlineMedium" fontWeight="semibold" color="$color" marginBottom="$md">
              Today&apos;s Hours
            </Text>
            
            <View
              backgroundColor="$background"
              borderRadius="$md"
              padding="$lg"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
            >
              <View flexDirection="row" alignItems="center" gap="$md">
                <AppIcons.clock size={24} color={todaySchedule.todayHours.isOpen ? "#34C759" : "#FF3B30"} />
                <View>
                  <Text fontSize="$bodyLarge" fontWeight="semibold" color="$color">
                    {todaySchedule.todayHours.isOpen ? 'Open' : 'Closed'}
                  </Text>
                  <Text fontSize="$bodyMedium" color="$colorSubtle">
                    {todaySchedule.todayHours.openTime} - {todaySchedule.todayHours.closeTime}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Error State */}
        {hasError && (
          <View
            backgroundColor="$backgroundSubtle"
            borderRadius="$md"
            padding="$lg"
            alignItems="center"
          >
            <AppIcons.warning size={48} color="#FF9500" />
            <Text fontSize="$bodyLarge" fontWeight="semibold" color="$color" marginTop="$md">
              Unable to load dashboard data
            </Text>
            <Text fontSize="$bodyMedium" color="$colorSubtle" textAlign="center" marginTop="$sm">
              Please check your connection and try again
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};
