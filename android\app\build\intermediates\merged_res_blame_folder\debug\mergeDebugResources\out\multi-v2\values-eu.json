{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-65:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\10c7247ae1e51dbe3d3ee369ed79f4bb\\transformed\\material-1.13.0-alpha10\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1510,1598,1661,1727,1791,1862,1925,1999,2075,2132,2186,2295,2354,2417,2471,2545,2670,2760,2838,2927,3010,3090,3235,3318,3400,3539,3630,3713,3765,3818,3884,3955,4035,4106,4186,4264,4342,4415,4490,4597,4684,4771,4862,4955,5027,5103,5195,5246,5328,5394,5478,5564,5626,5690,5753,5821,5928,6037,6133,6238,6336,6432,6488,6545,6628,6713,6790", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,78,87,62,65,63,70,62,73,75,56,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,138,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,97,95,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1505,1593,1656,1722,1786,1857,1920,1994,2070,2127,2181,2290,2349,2412,2466,2540,2665,2755,2833,2922,3005,3085,3230,3313,3395,3534,3625,3708,3760,3813,3879,3950,4030,4101,4181,4259,4337,4410,4485,4592,4679,4766,4857,4950,5022,5098,5190,5241,5323,5389,5473,5559,5621,5685,5748,5816,5923,6032,6128,6233,6331,6427,6483,6540,6623,6708,6785,6862"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,4900,4966,5033,6780,6848,6909,6988,7076,7139,7205,7269,7340,7403,7477,7553,7610,7664,7773,7832,7895,7949,8023,8148,8238,8316,8405,8488,8568,8713,8796,8878,9017,9108,9191,9243,9296,9362,9433,9513,9584,9664,9742,9820,9893,9968,10075,10162,10249,10340,10433,10505,10581,10673,10724,10806,10872,10956,11042,11104,11168,11231,11299,11406,11515,11611,11716,11814,11910,11966,12023,12189,12274,12351", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,78,87,62,65,63,70,62,73,75,56,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,138,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,97,95,55,56,82,84,76,76", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,4961,5028,5127,6843,6904,6983,7071,7134,7200,7264,7335,7398,7472,7548,7605,7659,7768,7827,7890,7944,8018,8143,8233,8311,8400,8483,8563,8708,8791,8873,9012,9103,9186,9238,9291,9357,9428,9508,9579,9659,9737,9815,9888,9963,10070,10157,10244,10335,10428,10500,10576,10668,10719,10801,10867,10951,11037,11099,11163,11226,11294,11401,11510,11606,11711,11809,11905,11961,12018,12101,12269,12346,12423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "49,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4707,5132,5234,5347", "endColumns": "99,101,112,104", "endOffsets": "4802,5229,5342,5447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,378,514,647,783,909,1066,1165,1309,1448", "endColumns": "110,92,118,135,132,135,125,156,98,143,138,138", "endOffsets": "161,254,373,509,642,778,904,1061,1160,1304,1443,1582"}, "to": {"startLines": "48,50,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4807,5452,5571,5707,5840,5976,6102,6259,6358,6502,6641", "endColumns": "110,92,118,135,132,135,125,156,98,143,138,138", "endOffsets": "4702,4895,5566,5702,5835,5971,6097,6254,6353,6497,6636,6775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79bf916755d3ffa67a1186a4c7c4c642\\transformed\\core-1.16.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,12428", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,12524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,12106", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,12184"}}]}]}